# متطلبات أساسية لأكاديمية القرآنية - الاستضافة المشتركة
# إصدارات مبسطة ومتوافقة مع معظم الاستضافات المشتركة

# Django Framework
Django==4.2.7

# قاعدة البيانات MySQL
PyMySQL==1.1.0

# API Framework
djangorestframework==3.14.0

# المصادقة والأمان
PyJWT==2.8.0

# HTTP Requests
requests==2.31.0

# معالجة التواريخ
pytz==2023.3

# معالجة الصور
Pillow==10.0.1

# الدفع الإلكتروني
stripe==7.8.0

# الملفات الثابتة
whitenoise==6.6.0

# النماذج
django-crispy-forms==2.1

# CORS Headers
django-cors-headers==4.3.1

# إدارة المتغيرات
python-decouple==3.8

# تحسين الأداء
django-extensions==3.2.3

# إدارة الوقت
python-dateutil==2.8.2

# تشفير البيانات
cryptography==41.0.7

# معالجة النصوص
chardet==5.2.0

# معالجة الـ HTML
beautifulsoup4==4.12.2

# تحسين النماذج
django-widget-tweaks==1.5.0

# إدارة الإعدادات
django-environ==0.11.2
