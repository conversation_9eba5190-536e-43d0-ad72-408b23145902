/**
 * إعدادات جودة مشاركة الشاشة المحسنة لـ Jitsi Meet
 * حل مشكلة الجودة الضعيفة عند مشاركة الشاشة
 */

class JitsiScreenshareQualityManager {
    constructor() {
        this.defaultSettings = {
            // إعدادات جودة مشاركة الشاشة الفائقة - أقصى ما يمكن
            screenshare: {
                bitrate: 12000000,     // 12 Mbps - أقصى جودة ممكنة
                frameRate: 60,         // 60 إطار في الثانية
                height: 2160,          // ارتفاع 4K
                width: 3840,           // عرض 4K
                maxBitrate: 15000000,  // حد أقصى 15 Mbps
                minBitrate: 8000000,   // حد أدنى 8 Mbps
                quality: 'ultra'       // جودة فائقة
            },
            
            // إعدادات الترميز المحسنة لأقصى جودة
            videoCodec: {
                preferredCodec: 'AV1',   // AV1 أحدث وأفضل ترميز
                fallbackCodecs: ['VP9', 'VP8', 'H264']
            },

            // إعدادات الشبكة لأقصى جودة
            network: {
                enableSimulcast: false,      // تعطيل simulcast لأقصى جودة
                enableP2P: false,            // تعطيل P2P للحصول على أقصى جودة
                adaptiveBitrate: false,      // تعطيل التكيف للحفاظ على أقصى جودة
                enableLayerSuspension: false, // منع تقليل الجودة
                maxFullResolutionParticipants: -1 // جميع المشاركين بأقصى جودة
            }
        };
    }
    
    /**
     * تطبيق إعدادات جودة مشاركة الشاشة على Jitsi API
     */
    applyScreenshareQuality(jitsiApi) {
        if (!jitsiApi) {
            console.error('❌ Jitsi API غير متوفر');
            return;
        }
        
        try {
            console.log('🎯 تطبيق إعدادات جودة مشاركة الشاشة...');
            
            // تطبيق إعدادات جودة الفيديو
            jitsiApi.executeCommand('setVideoQuality', 'high');
            
            // إعدادات مخصصة لمشاركة الشاشة
            const screenshareConfig = {
                constraints: {
                    video: {
                        width: { ideal: 1920, max: 1920 },
                        height: { ideal: 1080, max: 1080 },
                        frameRate: { ideal: 30, max: 30 }
                    }
                }
            };
            
            // مراقبة بدء مشاركة الشاشة
            jitsiApi.addEventListener('screenSharingStatusChanged', (event) => {
                if (event.on) {
                    console.log('🖥️ بدء مشاركة الشاشة - تطبيق إعدادات الجودة العالية');
                    this.optimizeScreenshareQuality(jitsiApi);
                } else {
                    console.log('🖥️ إيقاف مشاركة الشاشة');
                }
            });
            
            console.log('✅ تم تطبيق إعدادات جودة مشاركة الشاشة');
            
        } catch (error) {
            console.error('❌ خطأ في تطبيق إعدادات جودة مشاركة الشاشة:', error);
        }
    }
    
    /**
     * تحسين جودة مشاركة الشاشة عند بدء المشاركة
     */
    optimizeScreenshareQuality(jitsiApi) {
        try {
            console.log('🚀 تطبيق أقصى جودة ممكنة لمشاركة الشاشة...');

            // تطبيق أقصى جودة لمشاركة الشاشة
            jitsiApi.executeCommand('setVideoQuality', 'ultra');

            // تطبيق إعدادات 4K
            jitsiApi.executeCommand('setVideoConstraints', {
                'lastN': -1,  // عرض جميع المشاركين
                'selectedEndpoints': [],
                'onStageEndpoints': [],
                'defaultConstraints': {
                    'maxHeight': 2160,  // 4K
                    'maxWidth': 3840,   // 4K
                    'minHeight': 1440,  // 2K كحد أدنى
                    'minWidth': 2560,   // 2K كحد أدنى
                    'frameRate': 60     // 60 fps
                }
            });

            // تطبيق أقصى معدل بت
            jitsiApi.executeCommand('setVideoInputConstraints', {
                'maxBitrate': 15000000,  // 15 Mbps
                'minBitrate': 8000000,   // 8 Mbps كحد أدنى
                'frameRate': 60,         // 60 fps
                'quality': 'ultra'
            });

            // تعطيل جميع ميزات توفير عرض النطاق
            jitsiApi.executeCommand('setVideoConstraints', {
                'enableSimulcast': false,
                'enableLayerSuspension': false,
                'enableAutomaticLayerSelection': false,
                'enableAdaptiveSimulcast': false
            });

            // فرض استخدام أفضل ترميز متاح
            jitsiApi.executeCommand('setVideoCodec', 'AV1');

            // إعدادات إضافية لأقصى جودة
            jitsiApi.executeCommand('setDesktopSharingFrameRate', 60);

            console.log('✅ تم تطبيق أقصى جودة ممكنة لمشاركة الشاشة');
            console.log('📊 الإعدادات النهائية: 4K@60fps, 15Mbps, AV1/VP9');

        } catch (error) {
            console.warn('⚠️ لا يمكن تطبيق بعض إعدادات الجودة الفائقة:', error);

            // محاولة تطبيق إعدادات أقل كبديل
            try {
                jitsiApi.executeCommand('setVideoQuality', 'high');
                jitsiApi.executeCommand('setVideoConstraints', {
                    'defaultConstraints': {
                        'maxHeight': 1440,
                        'maxWidth': 2560,
                        'frameRate': 30
                    }
                });
                jitsiApi.executeCommand('setVideoInputConstraints', {
                    'maxBitrate': 8000000,
                    'frameRate': 30
                });
                console.log('✅ تم تطبيق جودة عالية كبديل (2K@30fps, 8Mbps)');
            } catch (fallbackError) {
                console.error('❌ فشل في تطبيق أي إعدادات جودة:', fallbackError);
            }
        }
    }
    
    /**
     * إنشاء إعدادات Jitsi محسنة لجودة مشاركة الشاشة
     */
    getOptimizedConfig() {
        return {
            // إعدادات مشاركة الشاشة عالية الجودة
            desktopSharingFrameRate: {
                min: 15,
                max: 30
            },
            
            // جودة الفيديو المحسنة
            videoQuality: {
                maxBitratesVideo: {
                    low: 200000,
                    standard: 500000,
                    high: 1500000,
                    ssHigh: 3000000  // 3 Mbps لمشاركة الشاشة
                },
                minHeightForQualityLvl: {
                    360: 'low',
                    720: 'standard',
                    1080: 'high'
                }
            },
            
            // إعدادات مشاركة الشاشة
            screenshare: {
                bitrate: 3000000,
                frameRate: 30,
                height: 1080,
                width: 1920
            },
            
            // تعطيل P2P لضمان جودة أفضل
            p2p: {
                enabled: false
            },
            
            // إعدادات الترميز
            videoCodecPreferenceOrder: ['VP9', 'VP8', 'H264'],
            
            // إعدادات البث
            enableSimulcast: false,  // تعطيل للحصول على جودة أفضل
            maxFullResolutionParticipants: 2,
            
            // إعدادات الشبكة
            enableLayerSuspension: false,  // تعطيل لضمان الجودة
            channelLastN: -1,
            
            // إعدادات مشاركة الشاشة المتقدمة
            desktopSharingChromeExtId: null,
            desktopSharingChromeDisabled: false,
            desktopSharingFirefoxDisabled: false,
            
            // إعدادات إضافية للجودة
            enableNoAudioDetection: false,
            enableNoisyMicDetection: false,
            startAudioOnly: false,
            startWithAudioMuted: false,
            startWithVideoMuted: false
        };
    }
    
    /**
     * مراقبة جودة مشاركة الشاشة
     */
    monitorScreenshareQuality(jitsiApi) {
        if (!jitsiApi) return;
        
        // مراقبة إحصائيات الاتصال
        jitsiApi.addEventListener('connectionStatsReceived', (stats) => {
            if (stats.connectionQuality) {
                console.log(`📊 جودة الاتصال: ${stats.connectionQuality.score}/5`);
                
                if (stats.connectionQuality.score < 3) {
                    console.warn('⚠️ جودة الاتصال ضعيفة - قد تتأثر جودة مشاركة الشاشة');
                }
            }
        });
        
        // مراقبة أحداث الجودة
        jitsiApi.addEventListener('videoQualityChanged', (event) => {
            console.log(`📹 تغيير جودة الفيديو: ${event.quality}`);
        });
    }
    
    /**
     * نصائح لتحسين جودة مشاركة الشاشة
     */
    getQualityTips() {
        return [
            '🌐 تأكد من سرعة إنترنت جيدة (5+ Mbps للرفع)',
            '🖥️ أغلق التطبيقات غير الضرورية لتوفير موارد النظام',
            '📱 استخدم كابل إنترنت بدلاً من WiFi إن أمكن',
            '🎯 شارك نافذة واحدة بدلاً من الشاشة الكاملة',
            '⚡ تأكد من أن المتصفح محدث لأحدث إصدار',
            '🔧 استخدم Chrome أو Firefox للحصول على أفضل أداء'
        ];
    }
}

// إنشاء مثيل عام
window.jitsiScreenshareQuality = new JitsiScreenshareQualityManager();

// تصدير للاستخدام في الوحدات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = JitsiScreenshareQualityManager;
}
