"""
ملف إعدادات الإنتاج لأكاديمية القرآنية
يحتوي على جميع الإعدادات المطلوبة للاستضافة المشتركة
"""

# إعدادات قاعدة البيانات
# يرجى تحديث هذه القيم حسب إعدادات cPanel الخاصة بك
DATABASE_CONFIG = {
    'NAME': 'your_cpanel_username_qurania',     # مثال: john_qurania
    'USER': 'your_cpanel_username_qurania',     # مثال: john_qurania
    'PASSWORD': 'your_database_password',        # كلمة مرور قاعدة البيانات
    'HOST': 'localhost',                         # عادة localhost
    'PORT': '3306',                             # منفذ MySQL الافتراضي
}

# إعدادات الدومين والموقع
DOMAIN_CONFIG = {
    'MAIN_DOMAIN': 'yourdomain.com',            # دومينك الرئيسي
    'WWW_DOMAIN': 'www.yourdomain.com',         # مع www
    'SUBDOMAIN': 'academy.yourdomain.com',      # subdomain إذا كنت تستخدمه
    'SITE_NAME': 'أكاديمية القرآنية',
    'SITE_DESCRIPTION': 'منصة تعليم القرآن الكريم أونلاين',
}

# إعدادات البريد الإلكتروني
EMAIL_CONFIG = {
    'EMAIL_HOST': 'mail.yourdomain.com',        # خادم البريد الخاص بدومينك
    'EMAIL_PORT': 587,                          # منفذ SMTP
    'EMAIL_USE_TLS': True,                      # تشفير TLS
    'EMAIL_HOST_USER': '<EMAIL>', # بريد إلكتروني من دومينك
    'EMAIL_HOST_PASSWORD': 'your_email_password', # كلمة مرور البريد
    'DEFAULT_FROM_EMAIL': 'أكاديمية القرآنية <<EMAIL>>',
    'ADMIN_EMAIL': '<EMAIL>',      # بريد المدير
}

# إعدادات الدفع الإلكتروني
PAYMENT_CONFIG = {
    # Stripe (للبطاقات الائتمانية)
    'STRIPE_PUBLISHABLE_KEY': 'pk_live_...',    # مفتاح Stripe العام للإنتاج
    'STRIPE_SECRET_KEY': 'sk_live_...',         # مفتاح Stripe السري للإنتاج
    'STRIPE_WEBHOOK_SECRET': 'whsec_...',       # سر webhook للإنتاج
    
    # PayPal
    'PAYPAL_CLIENT_ID': 'your_paypal_client_id',
    'PAYPAL_CLIENT_SECRET': 'your_paypal_client_secret',
    'PAYPAL_MODE': 'live',                      # 'live' للإنتاج، 'sandbox' للاختبار
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'SECRET_KEY': 'your-production-secret-key-change-this-to-random-string',
    'SECURE_SSL_REDIRECT': False,               # True إذا كان لديك SSL
    'SESSION_COOKIE_SECURE': False,             # True إذا كان لديك SSL
    'CSRF_COOKIE_SECURE': False,                # True إذا كان لديك SSL
    'SECURE_BROWSER_XSS_FILTER': True,
    'SECURE_CONTENT_TYPE_NOSNIFF': True,
    'X_FRAME_OPTIONS': 'SAMEORIGIN',
}

# إعدادات Jitsi Meet
JITSI_CONFIG = {
    'DEFAULT_DOMAIN': 'meet.ffmuc.net',         # خادم Jitsi الافتراضي
    'FALLBACK_DOMAINS': [
        '8x8.vc',
        'meet.element.io',
        'meet.jit.si'
    ],
    'ROOM_PREFIX': 'qurania_',                  # بادئة غرف الاجتماعات
    'ENABLE_RECORDING': False,                  # تسجيل الحصص
    'ENABLE_TRANSCRIPTION': False,              # تفريغ الحصص
    'MAX_PARTICIPANTS': 30,                     # أقصى عدد مشاركين
    'DEFAULT_QUALITY': 'standard',              # جودة افتراضية متوسطة
}

# إعدادات الملفات والتخزين
STORAGE_CONFIG = {
    'STATIC_URL': '/static/',
    'MEDIA_URL': '/media/',
    'FILE_UPLOAD_MAX_MEMORY_SIZE': 5 * 1024 * 1024,    # 5 MB
    'DATA_UPLOAD_MAX_MEMORY_SIZE': 5 * 1024 * 1024,    # 5 MB
    'FILE_UPLOAD_PERMISSIONS': 0o644,
}

# إعدادات التخزين المؤقت
CACHE_CONFIG = {
    'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
    'TIMEOUT': 300,                             # 5 دقائق
    'MAX_ENTRIES': 1000,
}

# إعدادات الجلسات
SESSION_CONFIG = {
    'SESSION_ENGINE': 'django.contrib.sessions.backends.db',
    'SESSION_COOKIE_AGE': 86400,                # 24 ساعة
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_SAVE_EVERY_REQUEST': False,
}

# إعدادات اللغة والمنطقة الزمنية
LOCALIZATION_CONFIG = {
    'LANGUAGE_CODE': 'ar',
    'TIME_ZONE': 'Asia/Riyadh',
    'USE_I18N': True,
    'USE_L10N': True,
    'USE_TZ': True,
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'LOG_LEVEL': 'ERROR',                       # فقط الأخطاء لتوفير المساحة
    'LOG_TO_FILE': True,
    'LOG_TO_CONSOLE': False,
    'MAX_LOG_SIZE': 10 * 1024 * 1024,          # 10 MB
}

# إعدادات الأداء
PERFORMANCE_CONFIG = {
    'USE_THOUSAND_SEPARATOR': False,
    'APPEND_SLASH': True,
    'PREPEND_WWW': False,
    'DATA_UPLOAD_MAX_NUMBER_FIELDS': 1000,
}

# إعدادات CORS للـ API
CORS_CONFIG = {
    'CORS_ALLOW_CREDENTIALS': True,
    'CORS_ALLOWED_ORIGINS': [
        "https://yourdomain.com",
        "https://www.yourdomain.com",
    ],
}

# إعدادات CSP (Content Security Policy)
CSP_CONFIG = {
    'CSP_DEFAULT_SRC': ("'self'",),
    'CSP_SCRIPT_SRC': (
        "'self'",
        "'unsafe-inline'",
        "'unsafe-eval'",
        "https://meet.ffmuc.net",
        "https://8x8.vc",
        "https://meet.jit.si",
        "https://js.stripe.com",
    ),
    'CSP_STYLE_SRC': (
        "'self'",
        "'unsafe-inline'",
        "https://fonts.googleapis.com",
    ),
    'CSP_IMG_SRC': (
        "'self'",
        "data:",
        "https:",
    ),
    'CSP_FRAME_SRC': (
        "'self'",
        "https://meet.ffmuc.net",
        "https://8x8.vc",
        "https://meet.jit.si",
        "https://js.stripe.com",
    ),
}

# دالة للحصول على الإعدادات
def get_database_config():
    """إرجاع إعدادات قاعدة البيانات"""
    return DATABASE_CONFIG

def get_email_config():
    """إرجاع إعدادات البريد الإلكتروني"""
    return EMAIL_CONFIG

def get_payment_config():
    """إرجاع إعدادات الدفع"""
    return PAYMENT_CONFIG

def get_security_config():
    """إرجاع إعدادات الأمان"""
    return SECURITY_CONFIG

def get_jitsi_config():
    """إرجاع إعدادات Jitsi"""
    return JITSI_CONFIG

# تحديث سريع للإعدادات
def update_domain(domain):
    """تحديث الدومين في جميع الإعدادات"""
    DOMAIN_CONFIG['MAIN_DOMAIN'] = domain
    DOMAIN_CONFIG['WWW_DOMAIN'] = f'www.{domain}'
    EMAIL_CONFIG['EMAIL_HOST'] = f'mail.{domain}'
    EMAIL_CONFIG['EMAIL_HOST_USER'] = f'noreply@{domain}'
    EMAIL_CONFIG['DEFAULT_FROM_EMAIL'] = f'أكاديمية القرآنية <noreply@{domain}>'
    EMAIL_CONFIG['ADMIN_EMAIL'] = f'admin@{domain}'
    CORS_CONFIG['CORS_ALLOWED_ORIGINS'] = [
        f"https://{domain}",
        f"https://www.{domain}",
    ]

# مثال على الاستخدام:
# update_domain('myacademy.com')

print("📋 تم تحميل إعدادات الإنتاج لأكاديمية القرآنية")
print("⚠️ يرجى تحديث الإعدادات حسب بيانات الاستضافة الخاصة بك")
