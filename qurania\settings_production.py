"""
إعدادات الإنتاج لـ PythonAnywhere
"""

from .settings import *
import os

# إعدادات الأمان للإنتاج
DEBUG = False

# المضيفين المسموح بهم
ALLOWED_HOSTS = [
    'elaspani.pythonanywhere.com',  # اسم المستخدم الصحيح
    'www.yourdomain.com',  # إذا كان لديك دومين مخصص
    'yourdomain.com',
]

# إعدادات قاعدة البيانات لـ PythonAnywhere
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'elaspani$qurania',  # اسم قاعدة البيانات الصحيح
        'USER': 'elaspani',  # اسم المستخدم الصحيح
        'PASSWORD': 'your_database_password',  # كلمة مرور قاعدة البيانات
        'HOST': 'elaspani.mysql.pythonanywhere-services.com',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# إعدادات الملفات الثابتة
STATIC_URL = '/static/'
STATIC_ROOT = '/home/<USER>/qurania/static'  # مسار الملفات الثابتة

MEDIA_URL = '/media/'
MEDIA_ROOT = '/home/<USER>/qurania/media'  # مسار ملفات الوسائط

# إعدادات الأمان
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# إعدادات الجلسات
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# إعدادات البريد الإلكتروني
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # أو خدمة البريد المفضلة لديك
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # بريدك الإلكتروني
EMAIL_HOST_PASSWORD = 'your_app_password'  # كلمة مرور التطبيق
DEFAULT_FROM_EMAIL = 'أكاديمية القرآنية <<EMAIL>>'

# إعدادات التخزين المؤقت
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# إعدادات السجلات
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/home/<USER>/qurania/logs/django.log',  # استبدل yourusername
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['file', 'console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'qurania': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# إعدادات Jitsi Meet للإنتاج
JITSI_SETTINGS = {
    'DEFAULT_DOMAIN': 'meet.ffmuc.net',  # خادم Jitsi المحسن
    'FALLBACK_DOMAINS': [
        '8x8.vc',
        'meet.element.io',
        'meet.jit.si'
    ],
    'ROOM_PREFIX': 'qurania_prod_',
    'ENABLE_RECORDING': False,  # تعطيل التسجيل في الإنتاج
    'ENABLE_TRANSCRIPTION': False,
    'MAX_PARTICIPANTS': 50,
    'DEFAULT_QUALITY': 'high',
}

# إعدادات الدفع
STRIPE_PUBLISHABLE_KEY = 'pk_live_...'  # مفتاح Stripe للإنتاج
STRIPE_SECRET_KEY = 'sk_live_...'  # مفتاح Stripe السري للإنتاج
STRIPE_WEBHOOK_SECRET = 'whsec_...'  # سر webhook للإنتاج

# إعدادات إضافية للأمان
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'your-production-secret-key-here')

# تعطيل debug toolbar في الإنتاج
if 'debug_toolbar' in INSTALLED_APPS:
    INSTALLED_APPS.remove('debug_toolbar')

if 'debug_toolbar.middleware.DebugToolbarMiddleware' in MIDDLEWARE:
    MIDDLEWARE.remove('debug_toolbar.middleware.DebugToolbarMiddleware')

# إعدادات المنطقة الزمنية
USE_TZ = True
TIME_ZONE = 'Asia/Riyadh'

# إعدادات اللغة
LANGUAGE_CODE = 'ar'
USE_I18N = True
USE_L10N = True

# إعدادات الملفات المرفوعة
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10 MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10 MB

# إعدادات الجلسات
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24 ساعة

# إعدادات CORS للـ API
CORS_ALLOWED_ORIGINS = [
    "https://yourusername.pythonanywhere.com",
    "https://www.yourdomain.com",
]

CORS_ALLOW_CREDENTIALS = True

# إعدادات CSP (Content Security Policy)
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = (
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    "https://meet.ffmuc.net",
    "https://8x8.vc",
    "https://meet.jit.si",
    "https://js.stripe.com",
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com",
)
CSP_FONT_SRC = (
    "'self'",
    "https://fonts.gstatic.com",
)
CSP_IMG_SRC = (
    "'self'",
    "data:",
    "https:",
)
CSP_CONNECT_SRC = (
    "'self'",
    "https://meet.ffmuc.net",
    "https://8x8.vc",
    "https://meet.jit.si",
    "wss://meet.ffmuc.net",
    "wss://8x8.vc",
    "wss://meet.jit.si",
)
CSP_FRAME_SRC = (
    "'self'",
    "https://meet.ffmuc.net",
    "https://8x8.vc",
    "https://meet.jit.si",
    "https://js.stripe.com",
)

# تعطيل بعض الميزات في الإنتاج
CELERY_TASK_ALWAYS_EAGER = False
CELERY_TASK_EAGER_PROPAGATES = False
