# 🕌 أكاديمية القرآنية - منصة تعليم القرآن الكريم

## 📋 نظرة عامة

أكاديمية القرآنية هي منصة تعليمية متكاملة لتعليم القرآن الكريم أونلاين مع ميزات متقدمة للحصص المباشرة والإدارة الشاملة.

## ✅ المشروع جاهز للنشر على Namecheap

### 📁 الملفات المهمة:
- `passenger_wsgi.py` - ملف WSGI للاستضافة المشتركة
- `.htaccess` - إعدادات الحماية والتوجيه
- `qurania/settings_shared_hosting.py` - إعدادات الإنتاج
- `requirements_basic.txt` - المتطلبات الأساسية
- `manage_production.py` - إدارة الإنتاج

### 🚀 خطوات النشر السريعة:

1. **تحديث الإعدادات:**
   - افتح `qurania/settings_shared_hosting.py`
   - غير `yourdomain.com` إلى دومينك
   - حدث إعدادات قاعدة البيانات

2. **ضغط المشروع:**
   - حدد جميع الملفات
   - انقر بزر الماوس الأيمن → "Send to" → "Compressed folder"

3. **رفع على Namecheap:**
   - cPanel → File Manager → public_html
   - رفع وفك ضغط الملف

4. **إعداد قاعدة البيانات:**
   - MySQL Databases في cPanel
   - إنشاء قاعدة بيانات ومستخدم

5. **تفعيل Python App:**
   - Python App في cPanel
   - تحديد `passenger_wsgi.py`

### 📖 أدلة مفصلة:
- `SIMPLE_DEPLOYMENT_GUIDE.md` - دليل النشر المبسط
- `DEPLOYMENT_CHECKLIST.md` - قائمة التحقق
- `PRODUCTION_READY.md` - دليل الجاهزية الكامل

## ✨ الميزات الرئيسية

### 👥 إدارة المستخدمين
- طلاب، معلمين، مدراء
- نظام صلاحيات متقدم
- ملفات شخصية شاملة

### 🎥 الحصص المباشرة
- تكامل Jitsi Meet بجودة 4K
- إدارة تلقائية لمدة الحصص
- مراقبة الحضور والغياب
- تقييم وتسجيل الحصص

### 💳 نظام الدفع
- Stripe للبطاقات الائتمانية
- PayPal للمحافظ الرقمية
- إدارة الاشتراكات والباقات
- فواتير تلقائية

### 📊 التقارير والإحصائيات
- تقارير شاملة للأداء
- إحصائيات الحضور
- تصدير البيانات (PDF/Excel)
- لوحات تحكم تفاعلية

## 🔧 الإعدادات المطلوب تحديثها

### في `qurania/settings_shared_hosting.py`:

```python
# الدومين
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']

# قاعدة البيانات
DATABASES = {
    'default': {
        'NAME': 'your_cpanel_username_qurania',
        'USER': 'your_cpanel_username_qurania',
        'PASSWORD': 'your_database_password',
    }
}

# البريد الإلكتروني
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your_email_password'
```

## 📱 التقنيات المستخدمة

- **Backend**: Django 4.2.7
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Video**: Jitsi Meet
- **Payment**: Stripe, PayPal
- **Security**: Django Security + Custom

## 🔒 الأمان

- حماية CSRF و XSS
- تشفير البيانات
- حماية ملفات النظام
- إعدادات أمان متقدمة
- مراقبة الوصول

## 🎯 الحالة الحالية

✅ **جاهز للإنتاج**  
✅ **محسن للاستضافة المشتركة**  
✅ **آمن ومحمي**  
✅ **موثق بالكامل**  
✅ **سهل النشر**  

## 📞 الدعم

للمساعدة في النشر:
- راجع `SIMPLE_DEPLOYMENT_GUIDE.md`
- راجع `PRODUCTION_READY.md`
- تحقق من `DEPLOYMENT_CHECKLIST.md`

---

**المشروع جاهز للضغط والرفع على Namecheap!** 🚀

**تم التطوير بواسطة**: Augment Agent  
**الحالة**: جاهز للإنتاج ✅
