{% extends 'base.html' %}
{% load static %}

{% block title %}{{ live_lesson.title }} - حصة مباشرة{% endblock %}

{% block extra_css %}
<style>
    .lesson-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .status-indicator {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .monitoring-panel {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 2px solid #0ea5e9;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .quality-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    
    .quality-card:hover {
        transform: translateY(-2px);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #047857 0%, #059669 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .btn-primary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }
    
    .btn-secondary {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification.success { background: #10b981; }
    .notification.warning { background: #f59e0b; }
    .notification.error { background: #ef4444; }
    .notification.info { background: #3b82f6; }
</style>
{% endblock %}

{% block content %}
<div class="lesson-container">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-2xl font-bold text-gray-800">
                <i class="fas fa-video text-green-600 ml-2"></i>
                {{ live_lesson.title }}
            </h1>
            <span class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                {{ live_lesson.get_status_display }}
            </span>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
                <i class="fas fa-user-graduate text-blue-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">المعلم</p>
                <p class="font-semibold">{{ live_lesson.teacher.get_full_name }}</p>
            </div>
            <div class="text-center">
                <i class="fas fa-clock text-orange-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">مدة الحصة</p>
                <p class="font-semibold">{{ live_lesson.duration_minutes }} دقيقة</p>
            </div>
            <div class="text-center">
                <i class="fas fa-calendar text-purple-500 text-2xl mb-2"></i>
                <p class="text-sm text-gray-600">موعد الحصة</p>
                <p class="font-semibold">{{ live_lesson.scheduled_date|date:"H:i" }}</p>
            </div>
        </div>
    </div>

    {% if live_lesson.status == 'scheduled' %}
    <!-- حالة الانتظار -->
    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
        <i class="fas fa-clock text-6xl text-yellow-400 mb-4"></i>
        <h3 class="text-xl font-bold text-gray-800 mb-2">في انتظار بدء الحصة</h3>
        <p class="text-gray-600 mb-4">المعلم {{ live_lesson.teacher.get_full_name }} لم يبدأ الحصة بعد</p>
        <p class="text-sm text-gray-500">
            <i class="fas fa-info-circle ml-1"></i>
            سيتم تحديث الصفحة تلقائياً عند بدء الحصة
        </p>
    </div>
    
    <script>
    // تحديث الصفحة كل 30 ثانية للتحقق من بدء الحصة
    setInterval(function() {
        location.reload();
    }, 30000);
    </script>
    
    {% else %}
    <!-- نظام الحضور والمراقبة الجديد -->
    <div class="monitoring-panel p-6 mb-6">
        <h4 class="text-lg font-bold text-gray-800 mb-4">
            <i class="fas fa-shield-alt text-green-600 ml-2"></i>
            نظام الحضور والمراقبة المتقدم
        </h4>
        
        <!-- حالة الحضور الحالية -->
        <div id="attendance-status" class="mb-4">
            <div class="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
                <div class="flex items-center">
                    <div id="status-indicator" class="w-3 h-3 rounded-full bg-gray-400 ml-3 status-indicator"></div>
                    <span id="status-text" class="font-medium text-gray-700">جاري التهيئة...</span>
                </div>
                <div id="attendance-timer" class="text-sm text-gray-600">
                    <i class="fas fa-stopwatch ml-1"></i>
                    <span id="timer-display">00:00:00</span>
                </div>
            </div>
        </div>

        <!-- حاوي Jitsi المدمج -->
        <div class="mb-6">
            <div class="bg-white rounded-lg p-4 shadow-sm">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="font-bold text-gray-800">
                        <i class="fas fa-video text-blue-600 ml-2"></i>
                        غرفة الحصة المباشرة
                    </h5>
                    <button id="start-jitsi-lesson" class="btn-primary">
                        <i class="fas fa-play ml-2"></i>
                        بدء الحصة
                    </button>
                </div>

                <!-- إطار Jitsi -->
                <div id="jitsi-container" style="width: 100%; height: 500px; border-radius: 8px; overflow: hidden; background: #000; display: none; border: 2px solid #e5e7eb;"></div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
            <button id="mark-attendance" class="btn-secondary" disabled>
                <i class="fas fa-check-circle ml-2"></i>
                تأكيد الحضور
            </button>

            <button id="mark-leave" class="btn-danger" disabled>
                <i class="fas fa-sign-out-alt ml-2"></i>
                تسجيل الخروج
            </button>
        </div>

        <!-- مؤشرات الجودة -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="quality-card">
                <i class="fas fa-heartbeat text-red-500 text-xl mb-2"></i>
                <p class="text-sm text-gray-600">إشارات الحياة</p>
                <p id="heartbeat-count" class="font-bold text-lg">0</p>
            </div>
            <div class="quality-card">
                <i class="fas fa-signal text-blue-500 text-xl mb-2"></i>
                <p class="text-sm text-gray-600">جودة الاتصال</p>
                <p id="connection-quality" class="font-bold text-lg">ممتاز</p>
            </div>
            <div class="quality-card">
                <i class="fas fa-star text-yellow-500 text-xl mb-2"></i>
                <p class="text-sm text-gray-600">نقاط الجودة</p>
                <p id="quality-score" class="font-bold text-lg">100</p>
            </div>
        </div>
    </div>

    <!-- تعليمات للطالب -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h5 class="font-bold text-blue-800 mb-2">
            <i class="fas fa-info-circle ml-2"></i>
            تعليمات الحضور المحدثة
        </h5>
        <ol class="text-sm text-blue-700 space-y-1">
            <li>1. اضغط على "بدء الحصة" لتشغيل Jitsi في هذه الصفحة</li>
            <li>2. انضم للحصة وفعل الكاميرا والمايك</li>
            <li>3. اضغط "تأكيد الحضور" بعد الانضمام للحصة</li>
            <li>4. ابق في الحصة لمدة 15 دقيقة على الأقل</li>
            <li>5. فعل الكاميرا مرتين على الأقل أثناء الحصة</li>
            <li>6. اضغط "تسجيل الخروج" عند انتهاء الحصة</li>
            <li>7. <strong>تحذير:</strong> الانقطاع أكثر من 5 دقائق سيلغي احتساب الحصة</li>
        </ol>
    </div>
    {% endif %}
</div>

<!-- نافذة التقييم الموحد -->
<div id="rating-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-10 mx-auto p-6 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="text-center">
            <h3 class="text-xl font-bold text-gray-900 mb-6">
                <i class="fas fa-star text-yellow-500 ml-2"></i>
                تقييم الحصة
            </h3>

            <form id="rating-form" class="space-y-6">
                {% csrf_token %}

                <!-- التقييم العام -->
                <div class="rating-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">التقييم العام للحصة</label>
                    <div class="star-rating" data-rating="overall_rating">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="overall_rating" id="overall_rating" required>
                </div>

                <!-- جودة المحتوى -->
                <div class="rating-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">جودة المحتوى ووضوح الشرح</label>
                    <div class="star-rating" data-rating="lesson_quality">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="lesson_quality" id="lesson_quality" required>
                </div>

                <!-- تفاعل المعلم -->
                <div class="rating-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">تفاعل المعلم واستجابته</label>
                    <div class="star-rating" data-rating="teacher_interaction">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="teacher_interaction" id="teacher_interaction" required>
                </div>

                <!-- الجودة التقنية -->
                <div class="rating-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الجودة التقنية (صوت، صورة، اتصال)</label>
                    <div class="star-rating" data-rating="technical_quality">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                    <input type="hidden" name="technical_quality" id="technical_quality" required>
                </div>

                <!-- تقييمات إضافية اختيارية -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- الالتزام بالوقت -->
                    <div class="rating-section">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الالتزام بالوقت (اختياري)</label>
                        <div class="star-rating" data-rating="punctuality">
                            <span class="star" data-value="1">★</span>
                            <span class="star" data-value="2">★</span>
                            <span class="star" data-value="3">★</span>
                            <span class="star" data-value="4">★</span>
                            <span class="star" data-value="5">★</span>
                        </div>
                        <input type="hidden" name="punctuality" id="punctuality">
                    </div>

                    <!-- التحضير للحصة -->
                    <div class="rating-section">
                        <label class="block text-sm font-medium text-gray-700 mb-2">التحضير للحصة (اختياري)</label>
                        <div class="star-rating" data-rating="lesson_preparation">
                            <span class="star" data-value="1">★</span>
                            <span class="star" data-value="2">★</span>
                            <span class="star" data-value="3">★</span>
                            <span class="star" data-value="4">★</span>
                            <span class="star" data-value="5">★</span>
                        </div>
                        <input type="hidden" name="lesson_preparation" id="lesson_preparation">
                    </div>
                </div>

                <!-- تعليق -->
                <div class="rating-section">
                    <label class="block text-sm font-medium text-gray-700 mb-2">تعليق إضافي (اختياري)</label>
                    <textarea
                        name="comment"
                        id="comment"
                        rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="شاركنا رأيك حول الحصة..."
                    ></textarea>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="flex justify-center space-x-4 pt-4">
                    <button type="submit" class="btn-primary px-6 py-2">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال التقييم
                    </button>
                    <button type="button" onclick="skipRating()" class="btn-secondary px-6 py-2">
                        <i class="fas fa-times ml-2"></i>
                        تخطي
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.star-rating {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin: 8px 0;
}

.star {
    font-size: 24px;
    color: #d1d5db;
    cursor: pointer;
    transition: color 0.2s;
    user-select: none;
}

.star:hover,
.star.active {
    color: #fbbf24;
}

.star.active {
    color: #f59e0b;
}

.rating-section {
    text-align: center;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.rating-section:hover {
    background: #f3f4f6;
}
</style>

{% csrf_token %}

<script>
// تمرير معرف الحصة ومعلومات المستخدم للـ JavaScript
window.lessonId = {{ live_lesson.id }};
window.userRole = 'student';
window.userEmail = '{{ request.user.email }}';
</script>
{% endblock %}

{% block extra_js %}
<!-- Jitsi Meet API من الخادم المحسن -->
<script src="https://meet.ffmuc.net/external_api.js"></script>

<!-- ملفات JavaScript المحدثة -->
<script src="{% static 'js/jitsi-servers.js' %}"></script>
<script src="{% static 'js/jitsi-integration.js' %}"></script>
<script src="{% static 'js/attendance_monitor.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نظام المراقبة المحسن
    const monitor = new RealTimeAttendanceMonitor(
        {{ live_lesson.id }},
        'live',
        '{{ request.user.get_full_name }}'
    );

    console.log('✅ تم تهيئة نظام المراقبة المحسن');
});
</script>
{% endblock %}
