/**
 * مدير مدة الحصة - نظام انتهاء الحصة التلقائي
 * يضمن انتهاء الحصة تلقائياً حسب المدة المحددة من قبل المدير
 */

class LessonDurationManager {
    constructor(lessonId, durationMinutes, lessonType = 'live') {
        this.lessonId = lessonId;
        this.durationMinutes = durationMinutes;
        this.lessonType = lessonType;
        this.startTime = null;
        this.endTime = null;
        this.timerInterval = null;
        this.warningShown = false;
        this.finalWarningShown = false;
        this.isActive = false;
        
        // أوقات التحذير (بالدقائق قبل الانتهاء)
        this.warningTimes = {
            first: 10,  // تحذير أول قبل 10 دقائق
            final: 2    // تحذير أخير قبل دقيقتين
        };
        
        console.log(`🕐 تم تهيئة مدير مدة الحصة: ${durationMinutes} دقيقة`);
    }
    
    /**
     * بدء مراقبة مدة الحصة
     */
    startDurationMonitoring() {
        if (this.isActive) {
            console.warn('⚠️ مراقبة المدة نشطة بالفعل');
            return;
        }
        
        this.startTime = new Date();
        this.endTime = new Date(this.startTime.getTime() + (this.durationMinutes * 60 * 1000));
        this.isActive = true;
        
        console.log(`⏰ بدء مراقبة مدة الحصة:`);
        console.log(`📅 وقت البدء: ${this.startTime.toLocaleTimeString()}`);
        console.log(`📅 وقت الانتهاء المتوقع: ${this.endTime.toLocaleTimeString()}`);
        console.log(`⏱️ المدة: ${this.durationMinutes} دقيقة`);
        
        // بدء العداد
        this.startTimer();
        
        // إرسال إشعار بدء المراقبة
        this.sendDurationUpdate('started');
    }
    
    /**
     * بدء العداد التنازلي
     */
    startTimer() {
        this.timerInterval = setInterval(() => {
            const now = new Date();
            const timeLeft = this.endTime - now;
            const minutesLeft = Math.floor(timeLeft / (1000 * 60));
            const secondsLeft = Math.floor((timeLeft % (1000 * 60)) / 1000);
            
            // تحديث العرض
            this.updateTimerDisplay(minutesLeft, secondsLeft);
            
            // التحقق من التحذيرات
            this.checkWarnings(minutesLeft);
            
            // التحقق من انتهاء الوقت
            if (timeLeft <= 0) {
                this.handleLessonTimeout();
            }
            
        }, 1000);
    }
    
    /**
     * تحديث عرض العداد
     */
    updateTimerDisplay(minutes, seconds) {
        const timerElement = document.getElementById('lesson-timer');
        if (timerElement) {
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            timerElement.textContent = timeString;
            
            // تغيير لون العداد حسب الوقت المتبقي
            if (minutes <= 2) {
                timerElement.className = 'lesson-timer critical';
            } else if (minutes <= 10) {
                timerElement.className = 'lesson-timer warning';
            } else {
                timerElement.className = 'lesson-timer normal';
            }
        }
        
        // تحديث عنوان الصفحة
        document.title = `${minutes}:${seconds.toString().padStart(2, '0')} - حصة مباشرة`;
    }
    
    /**
     * التحقق من التحذيرات
     */
    checkWarnings(minutesLeft) {
        // التحذير الأول (10 دقائق)
        if (minutesLeft <= this.warningTimes.first && !this.warningShown) {
            this.showWarning('first', minutesLeft);
            this.warningShown = true;
        }
        
        // التحذير الأخير (2 دقيقة)
        if (minutesLeft <= this.warningTimes.final && !this.finalWarningShown) {
            this.showWarning('final', minutesLeft);
            this.finalWarningShown = true;
        }
    }
    
    /**
     * إظهار تحذير انتهاء الوقت
     */
    showWarning(type, minutesLeft) {
        let message, title, icon;
        
        if (type === 'first') {
            title = 'تحذير: اقتراب انتهاء الحصة';
            message = `سيتم إنهاء الحصة تلقائياً خلال ${minutesLeft} دقيقة`;
            icon = 'fas fa-clock';
        } else if (type === 'final') {
            title = 'تحذير أخير: انتهاء الحصة قريباً';
            message = `سيتم إنهاء الحصة تلقائياً خلال ${minutesLeft} دقيقة`;
            icon = 'fas fa-exclamation-triangle';
        }
        
        // إظهار إشعار
        this.showNotification(message, 'warning', 5000);
        
        // إظهار مودال تحذير
        this.showWarningModal(title, message, icon, minutesLeft);
        
        // إرسال تحذير للخادم
        this.sendDurationUpdate('warning', { type, minutesLeft });
        
        console.warn(`⚠️ ${title}: ${message}`);
    }
    
    /**
     * معالجة انتهاء وقت الحصة
     */
    handleLessonTimeout() {
        console.log('⏰ انتهى وقت الحصة - بدء الإنهاء التلقائي');
        
        this.stopTimer();
        this.isActive = false;
        
        // إظهار رسالة انتهاء الوقت
        this.showTimeoutModal();
        
        // إنهاء الحصة تلقائياً
        this.autoEndLesson();
    }
    
    /**
     * إنهاء الحصة تلقائياً
     */
    async autoEndLesson() {
        try {
            console.log('🔄 إنهاء الحصة تلقائياً...');
            
            // إرسال طلب إنهاء الحصة
            const response = await fetch(`/api/live-lessons/${this.lessonId}/auto-end/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    reason: 'timeout',
                    duration_minutes: this.durationMinutes,
                    actual_duration: this.getActualDuration()
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                console.log('✅ تم إنهاء الحصة تلقائياً بنجاح');
                
                // إغلاق Jitsi
                if (window.jitsiApi) {
                    window.jitsiApi.executeCommand('hangup');
                }
                
                // إرسال تحديث نهائي
                this.sendDurationUpdate('ended', {
                    reason: 'timeout',
                    actual_duration: this.getActualDuration()
                });
                
                // إعادة التوجيه بعد 3 ثوانِ
                setTimeout(() => {
                    window.location.href = '/dashboard/';
                }, 3000);
                
            } else {
                console.error('❌ فشل في إنهاء الحصة تلقائياً:', data.message);
                this.showNotification('حدث خطأ في إنهاء الحصة تلقائياً', 'error');
            }
            
        } catch (error) {
            console.error('❌ خطأ في إنهاء الحصة تلقائياً:', error);
            this.showNotification('حدث خطأ في إنهاء الحصة تلقائياً', 'error');
        }
    }
    
    /**
     * إيقاف العداد
     */
    stopTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    /**
     * الحصول على المدة الفعلية للحصة
     */
    getActualDuration() {
        if (!this.startTime) return 0;
        const now = new Date();
        return Math.floor((now - this.startTime) / (1000 * 60)); // بالدقائق
    }
    
    /**
     * إرسال تحديث حالة المدة للخادم
     */
    async sendDurationUpdate(status, data = {}) {
        try {
            await fetch(`/api/live-lessons/${this.lessonId}/duration-update/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status,
                    duration_minutes: this.durationMinutes,
                    actual_duration: this.getActualDuration(),
                    timestamp: new Date().toISOString(),
                    ...data
                })
            });
        } catch (error) {
            console.warn('⚠️ فشل في إرسال تحديث المدة:', error);
        }
    }
    
    /**
     * إظهار مودال تحذير
     */
    showWarningModal(title, message, icon, minutesLeft) {
        const modal = document.createElement('div');
        modal.className = 'duration-warning-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content warning">
                    <div class="modal-header">
                        <i class="${icon}"></i>
                        <h3>${title}</h3>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                        <div class="countdown-display">
                            <span class="countdown-number">${minutesLeft}</span>
                            <span class="countdown-label">دقيقة متبقية</span>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button onclick="this.closest('.duration-warning-modal').remove()" class="btn-acknowledge">
                            فهمت
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // إزالة المودال تلقائياً بعد 10 ثوانِ
        setTimeout(() => {
            if (modal.parentNode) {
                modal.remove();
            }
        }, 10000);
    }
    
    /**
     * إظهار مودال انتهاء الوقت
     */
    showTimeoutModal() {
        const modal = document.createElement('div');
        modal.className = 'duration-timeout-modal';
        modal.innerHTML = `
            <div class="modal-overlay">
                <div class="modal-content timeout">
                    <div class="modal-header">
                        <i class="fas fa-clock"></i>
                        <h3>انتهى وقت الحصة</h3>
                    </div>
                    <div class="modal-body">
                        <p>تم انتهاء المدة المحددة للحصة (${this.durationMinutes} دقيقة)</p>
                        <p>سيتم إنهاء الحصة تلقائياً وإعادة توجيهك للوحة التحكم</p>
                        <div class="loading-spinner"></div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
    }
    
    /**
     * إظهار إشعار
     */
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `duration-notification ${type}`;
        notification.innerHTML = `
            <i class="fas fa-clock"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
    
    /**
     * إضافة عداد المدة للواجهة
     */
    addTimerToUI() {
        const timerContainer = document.createElement('div');
        timerContainer.className = 'lesson-timer-container';
        timerContainer.innerHTML = `
            <div class="timer-label">الوقت المتبقي:</div>
            <div id="lesson-timer" class="lesson-timer normal">
                ${this.durationMinutes.toString().padStart(2, '0')}:00
            </div>
        `;
        
        // إضافة العداد لرأس الصفحة
        const header = document.querySelector('.lesson-header .lesson-controls');
        if (header) {
            header.insertBefore(timerContainer, header.firstChild);
        }
    }
}

// تصدير الكلاس
window.LessonDurationManager = LessonDurationManager;
