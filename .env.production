# متغيرات البيئة للإنتاج على PythonAnywhere

# إعدادات Django الأساسية
DEBUG=False
SECRET_KEY=your-production-secret-key-here-change-this
DJANGO_SETTINGS_MODULE=qurania.settings_production

# إعدادات قاعدة البيانات
DB_NAME=elaspani$qurania
DB_USER=elaspani
DB_PASSWORD=your_database_password
DB_HOST=elaspani.mysql.pythonanywhere-services.com
DB_PORT=3306

# إعدادات المضيفين المسموح بهم
ALLOWED_HOSTS=elaspani.pythonanywhere.com,www.yourdomain.com

# إعدادات البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
DEFAULT_FROM_EMAIL=أكاديمية القرآنية <<EMAIL>>

# إعدادات Stripe للدفع
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# إعدادات PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=live

# إعدادات الملفات
STATIC_ROOT=/home/<USER>/qurania/static
MEDIA_ROOT=/home/<USER>/qurania/media
STATIC_URL=/static/
MEDIA_URL=/media/

# إعدادات Jitsi Meet
JITSI_DEFAULT_DOMAIN=meet.ffmuc.net
JITSI_ROOM_PREFIX=qurania_prod_
JITSI_ENABLE_RECORDING=False
JITSI_MAX_PARTICIPANTS=50

# إعدادات الأمان
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# إعدادات السجلات
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/qurania/logs/django.log

# إعدادات المنطقة الزمنية
TIME_ZONE=Asia/Riyadh
LANGUAGE_CODE=ar

# إعدادات التخزين المؤقت
CACHE_BACKEND=django.core.cache.backends.locmem.LocMemCache

# إعدادات Celery (إذا كنت تستخدمها)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# إعدادات إضافية
MAX_UPLOAD_SIZE=10485760
SESSION_COOKIE_AGE=86400
