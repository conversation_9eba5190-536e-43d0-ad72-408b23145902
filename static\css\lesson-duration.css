/* أنماط مدير مدة الحصة */

/* عداد الوقت المتبقي */
.lesson-timer-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.timer-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.lesson-timer {
    font-size: 1.2rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.lesson-timer.normal {
    background: #10b981;
    color: white;
}

.lesson-timer.warning {
    background: #f59e0b;
    color: white;
    animation: pulse-warning 2s infinite;
}

.lesson-timer.critical {
    background: #ef4444;
    color: white;
    animation: pulse-critical 1s infinite;
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes pulse-critical {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

/* مودال التحذير */
.duration-warning-modal,
.duration-timeout-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 400px;
    width: 90%;
    animation: modal-appear 0.3s ease-out;
}

.modal-content.warning {
    border-top: 4px solid #f59e0b;
}

.modal-content.timeout {
    border-top: 4px solid #ef4444;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.modal-content.warning .modal-header i {
    color: #f59e0b;
}

.modal-content.timeout .modal-header i {
    color: #ef4444;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: bold;
    color: #1f2937;
}

.modal-body {
    padding: 1rem 1.5rem;
    text-align: center;
}

.modal-body p {
    margin: 0 0 1rem;
    color: #6b7280;
    line-height: 1.5;
}

.countdown-display {
    background: #f3f4f6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}

.countdown-number {
    font-size: 2rem;
    font-weight: bold;
    color: #ef4444;
    display: block;
}

.countdown-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem;
    text-align: center;
}

.btn-acknowledge {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s;
}

.btn-acknowledge:hover {
    background: #2563eb;
}

/* إشعارات المدة */
.duration-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    animation: notification-slide-in 0.3s ease-out;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 300px;
}

.duration-notification.info {
    background: #3b82f6;
}

.duration-notification.warning {
    background: #f59e0b;
}

.duration-notification.error {
    background: #ef4444;
}

.duration-notification.fade-out {
    animation: notification-slide-out 0.3s ease-in;
}

@keyframes notification-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes notification-slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* سبينر التحميل */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .lesson-timer-container {
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.5rem;
    }
    
    .timer-label {
        font-size: 0.8rem;
    }
    
    .lesson-timer {
        font-size: 1rem;
    }
    
    .modal-content {
        margin: 1rem;
        max-width: none;
    }
    
    .duration-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* تأثيرات إضافية */
.lesson-timer-container:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.modal-content {
    transform-origin: center;
}

.duration-notification i {
    flex-shrink: 0;
}

/* حالة انتهاء الوقت */
.timeout-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(239, 68, 68, 0.9);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    animation: timeout-appear 0.5s ease-out;
}

.timeout-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.timeout-content p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

@keyframes timeout-appear {
    from {
        opacity: 0;
        background: rgba(239, 68, 68, 0);
    }
    to {
        opacity: 1;
        background: rgba(239, 68, 68, 0.9);
    }
}
