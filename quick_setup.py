#!/usr/bin/env python3
"""
إعداد سريع لأكاديمية القرآنية للإنتاج
"""

import os
import sys

def create_production_folders():
    """إنشاء المجلدات المطلوبة للإنتاج"""
    folders = [
        'static',
        'media', 
        'logs',
        'cache',
        'backups'
    ]
    
    for folder in folders:
        os.makedirs(folder, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {folder}")

def create_index_html():
    """إنشاء صفحة index.html للحماية"""
    content = """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>أكاديمية القرآنية</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            text-align: center; 
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .logo { 
            font-size: 3em; 
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .message { 
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        .loading {
            border: 4px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="logo">🕌 أكاديمية القرآنية</div>
    <div class="message">الموقع قيد الإعداد...</div>
    <div class="loading"></div>
    <p>سيتم تشغيل الموقع قريباً إن شاء الله</p>
</body>
</html>"""
    
    with open('index.html', 'w', encoding='utf-8') as f:
        f.write(content)
    print("✅ تم إنشاء صفحة index.html")

def create_robots_txt():
    """إنشاء ملف robots.txt"""
    content = """User-agent: *
Disallow: /admin/
Disallow: /api/
Disallow: /media/private/
Allow: /static/
Allow: /media/public/

Sitemap: https://yourdomain.com/sitemap.xml
"""
    
    with open('robots.txt', 'w') as f:
        f.write(content)
    print("✅ تم إنشاء ملف robots.txt")

def update_settings_file():
    """تحديث ملف الإعدادات بالقيم الافتراضية"""
    try:
        # قراءة ملف الإعدادات
        with open('qurania/settings_shared_hosting.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحديث بعض القيم الافتراضية
        content = content.replace(
            "SECRET_KEY = 'your-production-secret-key-change-this'",
            "SECRET_KEY = 'qurania-academy-2024-change-this-key-in-production'"
        )
        
        # حفظ الملف المحدث
        with open('qurania/settings_shared_hosting.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث ملف الإعدادات")
    except Exception as e:
        print(f"⚠️ تعذر تحديث ملف الإعدادات: {e}")

def create_deployment_checklist():
    """إنشاء قائمة تحقق للنشر"""
    checklist = """# قائمة تحقق نشر أكاديمية القرآنية

## قبل الرفع:
- [ ] تحديث الدومين في settings_shared_hosting.py
- [ ] تحديث إعدادات قاعدة البيانات
- [ ] تحديث إعدادات البريد الإلكتروني
- [ ] تحديث مفاتيح الدفع (Stripe/PayPal)
- [ ] تغيير SECRET_KEY
- [ ] ضغط الملفات

## بعد الرفع:
- [ ] إنشاء قاعدة البيانات في cPanel
- [ ] تفعيل Python App
- [ ] تثبيت المتطلبات
- [ ] تشغيل الهجرات
- [ ] جمع الملفات الثابتة
- [ ] إنشاء مستخدم مدير
- [ ] اختبار الموقع

## الاختبارات:
- [ ] تسجيل الدخول للإدارة
- [ ] إنشاء حصة مباشرة
- [ ] اختبار Jitsi Meet
- [ ] اختبار نظام الدفع
- [ ] اختبار البريد الإلكتروني

## الأمان:
- [ ] تغيير كلمات المرور الافتراضية
- [ ] تفعيل SSL (إذا متاح)
- [ ] فحص صلاحيات الملفات
- [ ] إعداد نسخ احتياطية

## الأداء:
- [ ] تفعيل التخزين المؤقت
- [ ] ضغط الملفات الثابتة
- [ ] مراقبة استخدام الموارد
"""
    
    with open('DEPLOYMENT_CHECKLIST.md', 'w', encoding='utf-8') as f:
        f.write(checklist)
    print("✅ تم إنشاء قائمة تحقق النشر")

def main():
    """الدالة الرئيسية"""
    print("🚀 إعداد سريع لأكاديمية القرآنية للإنتاج")
    print("=" * 50)
    
    # تشغيل جميع خطوات الإعداد
    create_production_folders()
    create_index_html()
    create_robots_txt()
    update_settings_file()
    create_deployment_checklist()
    
    print("\n" + "=" * 50)
    print("🎉 تم الإعداد السريع بنجاح!")
    print("=" * 50)
    
    print("\n📋 الخطوات التالية:")
    print("1. حدث الإعدادات في qurania/settings_shared_hosting.py")
    print("2. حدث الإعدادات في production_config.py")
    print("3. اضغط المشروع وارفعه على Namecheap")
    print("4. اتبع DEPLOYMENT_CHECKLIST.md")
    
    print("\n📁 الملفات المهمة:")
    print("- passenger_wsgi.py (ملف WSGI)")
    print("- .htaccess (حماية وتحسين)")
    print("- requirements_basic.txt (المتطلبات الأساسية)")
    print("- manage_production.py (إدارة الإنتاج)")

if __name__ == '__main__':
    main()
