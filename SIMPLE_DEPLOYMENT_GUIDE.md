# 🚀 دليل النشر المبسط - أكاديمية القرآنية على Namecheap

## 📋 الخطوات السريعة

### 1. تحضير المشروع محلياً

```bash
# تشغيل الإعداد السريع
python quick_setup.py
```

### 2. تحديث الإعدادات

**في ملف `qurania/settings_shared_hosting.py`:**
```python
# غير هذه القيم
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']

DATABASES = {
    'default': {
        'NAME': 'your_cpanel_username_qurania',
        'USER': 'your_cpanel_username_qurania', 
        'PASSWORD': 'your_database_password',
    }
}

EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your_email_password'
```

### 3. ض<PERSON><PERSON> المشروع

**في Windows:**
1. حدد جميع ملفات المشروع
2. انقر بزر الماوس الأيمن
3. اختر "Send to" → "Compressed folder"
4. اسم الملف: `qurania_academy.zip`

### 4. رفع على Namecheap

1. **تسجيل الدخول لـ cPanel**
2. **File Manager** → **public_html**
3. **Upload** → ارفع `qurania_academy.zip`
4. **Extract** → فك الضغط

### 5. إعداد قاعدة البيانات

1. **MySQL Databases** في cPanel
2. **Create Database:** `qurania`
3. **Create User:** `qurania_user`
4. **Add User to Database** مع جميع الصلاحيات

### 6. إعداد Python App

1. **Python App** في cPanel
2. **Create Application:**
   - Python Version: 3.9+
   - Application Root: `/public_html`
   - Application URL: `/`
   - Startup File: `passenger_wsgi.py`

### 7. تثبيت المتطلبات

**في Python App → Packages:**
```
Django==4.2.7
PyMySQL==1.1.0
djangorestframework==3.14.0
PyJWT==2.8.0
requests==2.31.0
Pillow==10.0.1
stripe==7.8.0
```

### 8. تشغيل الأوامر

**في Terminal (إذا متاح):**
```bash
cd /home/<USER>/public_html
python manage_production.py migrate
python manage_production.py collectstatic --noinput
python manage_production.py createsuperuser
```

### 9. اختبار الموقع

- **الموقع:** https://yourdomain.com
- **الإدارة:** https://yourdomain.com/admin/

## 🔧 إعدادات مهمة

### تحديث الدومين:
```python
ALLOWED_HOSTS = ['yourdomain.com', 'www.yourdomain.com']
```

### إعدادات قاعدة البيانات:
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'yourusername_qurania',
        'USER': 'yourusername_qurania',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

### إعدادات البريد:
```python
EMAIL_HOST = 'mail.yourdomain.com'
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your_email_password'
```

## 🚨 مشاكل شائعة

### خطأ 500:
- تحقق من Error Log في cPanel
- تأكد من صحة إعدادات قاعدة البيانات

### مشاكل قاعدة البيانات:
```bash
# في Terminal
python manage_production.py check --database default
```

### مشاكل الملفات الثابتة:
```bash
python manage_production.py collectstatic --clear
```

## 📞 الدعم

### فحص الحالة:
```bash
python manage_production.py check
```

### فحص قاعدة البيانات:
```bash
python manage_production.py dbshell
```

## 🎉 تهانينا!

إذا اتبعت الخطوات، فموقعك الآن يعمل على Namecheap!

**لا تنس:**
- تغيير كلمات المرور الافتراضية
- إعداد SSL إذا كان متاحاً
- عمل نسخة احتياطية دورية
