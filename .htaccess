# .htaccess لأكاديمية القرآنية - Namecheap استضافة مشتركة

# تفعيل إعادة الكتابة
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS (إذا كان لديك SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www إلى non-www (اختياري)
# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# حماية ملفات Python
<Files "*.py">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات الإعدادات
<Files "*.ini">
    Order allow,deny
    Deny from all
</Files>

<Files "*.cfg">
    Order allow,deny
    Deny from all
</Files>

<Files "*.conf">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات قاعدة البيانات
<Files "*.db">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sqlite*">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات السجلات
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات النسخ الاحتياطية
<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "*.backup">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات Git
<Files ".git*">
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات البيئة
<Files ".env*">
    Order allow,deny
    Deny from all
</Files>

# حماية requirements.txt
<Files "requirements.txt">
    Order allow,deny
    Deny from all
</Files>

# حماية manage.py
<Files "manage.py">
    Order allow,deny
    Deny from all
</Files>

# السماح بالوصول للملفات الثابتة
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    Order allow,deny
    Allow from all
    
    # تفعيل الضغط
    <IfModule mod_deflate.c>
        SetOutputFilter DEFLATE
        SetEnvIfNoCase Request_URI \
            \.(?:gif|jpe?g|png)$ no-gzip dont-vary
        SetEnvIfNoCase Request_URI \
            \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    </IfModule>
    
    # تعيين انتهاء الصلاحية للملفات الثابتة
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/ico "access plus 1 month"
        ExpiresByType image/svg+xml "access plus 1 month"
    </IfModule>
</FilesMatch>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع MIME type sniffing
    Header set X-Content-Type-Options nosniff
    
    # تفعيل XSS protection
    Header set X-XSS-Protection "1; mode=block"
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# تحسين الأداء
<IfModule mod_deflate.c>
    # ضغط النصوص
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# منع الوصول للمجلدات الحساسة
RedirectMatch 403 ^.*/\.(git|svn|env)(/.*)?$
RedirectMatch 403 ^.*/(logs|cache|backups)(/.*)?$

# صفحات الأخطاء المخصصة
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html
ErrorDocument 403 /403.html

# تحديد الترميز
AddDefaultCharset UTF-8

# تحسين الذاكرة
php_value memory_limit 256M
php_value max_execution_time 300
php_value max_input_time 300
php_value post_max_size 20M
php_value upload_max_filesize 10M

# تعطيل عرض الأخطاء في الإنتاج
php_flag display_errors Off
php_flag log_errors On

# إعدادات الجلسات
php_value session.gc_maxlifetime 86400
php_value session.cookie_lifetime 86400
