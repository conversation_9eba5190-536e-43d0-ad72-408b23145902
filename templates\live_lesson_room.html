{% extends 'base.html' %}
{% load static %}

{% block title %}{{ live_lesson.title }} - حصة مباشرة{% endblock %}

{% block extra_css %}
<style>
    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
    }

    .live-lesson-container {
        height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .lesson-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .jitsi-container {
        flex: 1;
        position: relative;
        background: #000;
    }

    #jitsi-meet {
        width: 100%;
        height: 100%;
        border: none;
        background: #000;
        min-height: 600px; /* ضمان الحد الأدنى للارتفاع */
    }

    /* ضمان أن iframe داخل Jitsi يستخدم الارتفاع الكامل */
    #jitsi-meet iframe {
        width: 100% !important;
        height: 100% !important;
        min-height: 600px !important;
        border: none !important;
    }

    .jitsi-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: #000;
    }

    .lesson-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .lesson-details h1 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: bold;
    }

    .lesson-meta {
        font-size: 0.9rem;
        opacity: 0.9;
        margin-top: 0.25rem;
    }

    .lesson-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .control-btn {
        padding: 0.5rem 1rem;
        border: 2px solid rgba(255,255,255,0.3);
        background: rgba(255,255,255,0.1);
        color: white;
        border-radius: 0.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .control-btn:hover {
        background: rgba(255,255,255,0.2);
        border-color: rgba(255,255,255,0.5);
        color: white;
        text-decoration: none;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-live {
        background: #ef4444;
        color: white;
        animation: pulse 2s infinite;
    }

    .status-scheduled {
        background: #f59e0b;
        color: white;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #000;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        z-index: 100;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255,255,255,0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
        .lesson-info {
            flex-direction: column;
            align-items: flex-start;
        }

        .lesson-controls {
            width: 100%;
            justify-content: flex-start;
        }

        .lesson-details h1 {
            font-size: 1.25rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="live-lesson-container">
    <!-- Header -->
    <div class="lesson-header">
        <div class="lesson-info">
            <div class="lesson-details">
                <h1>{{ live_lesson.title }}</h1>
                <div class="lesson-meta">
                    {% if user.user_type == 'teacher' %}
                        <i class="fas fa-user-graduate ml-1"></i>
                        الطالب: {{ live_lesson.student.get_full_name }}
                    {% elif user.user_type == 'student' %}
                        <i class="fas fa-chalkboard-teacher ml-1"></i>
                        المعلم: {{ live_lesson.teacher.get_full_name }}
                    {% else %}
                        <i class="fas fa-users ml-1"></i>
                        {{ live_lesson.teacher.get_full_name }} مع {{ live_lesson.student.get_full_name }}
                    {% endif %}

                    <span class="mx-2">•</span>
                    <i class="fas fa-clock ml-1"></i>
                    {{ live_lesson.duration_minutes }} دقيقة

                    {% if live_lesson.started_at %}
                        <span class="mx-2">•</span>
                        <i class="fas fa-play ml-1"></i>
                        بدأت: {{ live_lesson.started_at|date:"H:i" }}
                    {% endif %}
                </div>
            </div>

            <div class="lesson-controls">
                <span class="status-badge {% if live_lesson.status == 'live' %}status-live{% else %}status-scheduled{% endif %}">
                    {% if live_lesson.status == 'live' %}
                        <i class="fas fa-circle ml-1"></i>
                        مباشرة الآن
                    {% else %}
                        <i class="fas fa-clock ml-1"></i>
                        {{ live_lesson.get_status_display }}
                    {% endif %}
                </span>

                {% if user.is_admin %}
                <a href="{% url 'admin_live_lessons' %}" class="control-btn">
                    <i class="fas fa-arrow-right ml-1"></i>
                    العودة
                </a>
                {% endif %}

                {% if user.user_type == 'teacher' and live_lesson.status == 'scheduled' %}
                <button onclick="startLesson()" class="control-btn">
                    <i class="fas fa-play ml-1"></i>
                    بدء الحصة
                </button>
                {% endif %}

                {% if user.user_type == 'teacher' and live_lesson.status == 'live' %}
                <button onclick="endLesson()" class="control-btn">
                    <i class="fas fa-stop ml-1"></i>
                    إنهاء الحصة
                </button>
                {% endif %}

                {% if user.is_admin and live_lesson.status == 'live' %}
                <button onclick="endLesson()" class="control-btn danger">
                    <i class="fas fa-stop ml-1"></i>
                    إنهاء الحصة
                </button>
                <button onclick="forceStopLesson()" class="control-btn danger" style="background-color: #dc2626;">
                    <i class="fas fa-ban ml-1"></i>
                    إيقاف فوري
                </button>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Jitsi Meet Container -->
    <div class="jitsi-container">
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            {% if user.is_admin %}
                <h3>جاري تحميل غرفة الاجتماع...</h3>
                <p>يرجى الانتظار بينما نقوم بإعداد الحصة المباشرة</p>
                <div style="margin-top: 1rem; opacity: 0.7;">
                    <i class="fas fa-shield-alt ml-1"></i>
                    مراقبة آمنة ومشفرة
                </div>
            {% elif user.user_type == 'teacher' %}
                <h3>جاري تحضير غرفة الحصة...</h3>
                <p>يرجى الانتظار بينما نقوم بإعداد الحصة المباشرة</p>
                <div style="margin-top: 1rem; opacity: 0.7;">
                    <i class="fas fa-chalkboard-teacher ml-1"></i>
                    غرفة تعليمية متقدمة
                </div>
            {% elif user.user_type == 'student' %}
                <h3>جاري الانضمام للحصة...</h3>
                <p>يرجى الانتظار بينما نقوم بتوصيلك بالمعلم</p>
                <div style="margin-top: 1rem; opacity: 0.7;">
                    <i class="fas fa-user-graduate ml-1"></i>
                    بيئة تعلم آمنة
                </div>
            {% endif %}
        </div>
        <!-- حاوي Jitsi Meet API -->
        <div id="jitsi-meet" style="width: 100%; height: 100%; background: #000;"></div>
    </div>
</div>

<!-- Jitsi Meet API من الخادم المحسن -->
<script src="https://meet.ffmuc.net/external_api.js"></script>

<!-- ملفات JavaScript المحدثة -->
<script src="{% static 'js/jitsi-servers.js' %}"></script>
<script src="{% static 'js/jitsi-screenshare-quality.js' %}"></script>
<script src="{% static 'js/jitsi-integration.js' %}"></script>
<script src="{% static 'js/attendance_monitor.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if user.is_admin %}
    // تهيئة نظام المراقبة للمدير
    const monitor = new RealTimeAttendanceMonitor(
        {{ live_lesson.id }},
        'live',
        'مدير النظام (مراقب)'
    );

    // تخصيص حاوي Jitsi للمدير
    monitor.jitsiContainerId = 'jitsi-meet';
    monitor.isAdminMode = true;

    // بدء Jitsi تلقائياً للمدير
    setTimeout(() => {
        monitor.startJitsiLesson();
        // إخفاء overlay التحميل
        setTimeout(() => {
            document.getElementById('loading-overlay').style.display = 'none';
            showNotification('تم الدخول لمراقبة Jitsi Meet - مرحباً بك في غرفة المراقبة');
        }, 3000);
    }, 2000);

    console.log('✅ تم تهيئة نظام المراقبة للمدير');
    {% else %}
    // للمعلمين والطلاب - استخدام iframe العادي
    const iframe = document.getElementById('jitsi-iframe');
    if (iframe) {
        iframe.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loading-overlay').style.display = 'none';
                {% if user.user_type == 'teacher' %}
                showNotification('تم تحميل Jitsi Meet بنجاح - مرحباً بك في الفصل الافتراضي');
                {% elif user.user_type == 'student' %}
                showNotification('تم الانضمام لـ Jitsi Meet بنجاح - مرحباً بك في الفصل الافتراضي');
                {% endif %}
            }, 2000);
        });
    }
    {% endif %}
});



function startLesson() {
    fetch(`/api/live-lessons/{{ live_lesson.id }}/start/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء بدء الحصة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء بدء الحصة');
    });
}

function endLesson() {
    if (confirm('هل أنت متأكد من إنهاء الحصة؟ سيتم إخراج جميع المشاركين.')) {
        fetch(`/api/live-lessons/{{ live_lesson.id }}/end/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (window.jitsiApi) {
                    window.jitsiApi.executeCommand('hangup');
                }
                setTimeout(() => {
                    window.location.href = '/dashboard/';
                }, 1000);
            } else {
                alert('حدث خطأ أثناء إنهاء الحصة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إنهاء الحصة');
        });
    }
}

function forceStopLesson() {
    if (confirm('هل أنت متأكد من الإيقاف الفوري للحصة؟ سيتم إخراج جميع المشاركين فوراً بدون تأكيد إضافي.')) {
        fetch(`/api/live-lessons/{{ live_lesson.id }}/end/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق Jitsi فوراً
                if (window.jitsiApi) {
                    window.jitsiApi.executeCommand('hangup');
                }

                // إظهار رسالة وإعادة التوجيه
                alert('تم إيقاف الحصة فوراً');
                window.location.href = '{% url "admin_live_lessons" %}';
            } else {
                alert('حدث خطأ أثناء إيقاف الحصة: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إيقاف الحصة');
        });
    }
}

// دالة عرض الإشعارات
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-20 right-4 bg-purple-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-slide-in';
    notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('animate-slide-out');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// منع إغلاق الصفحة بدون تأكيد
window.addEventListener('beforeunload', function(e) {
    {% if user.user_type == 'teacher' and live_lesson.status == 'live' %}
    e.preventDefault();
    e.returnValue = 'هل أنت متأكد من مغادرة الحصة؟ سيتم إنهاؤها تلقائياً.';
    {% endif %}
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes slide-in {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slide-out {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    .animate-slide-in { animation: slide-in 0.3s ease-out; }
    .animate-slide-out { animation: slide-out 0.3s ease-in; }
`;
document.head.appendChild(style);
</script>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}
{% endblock %}
