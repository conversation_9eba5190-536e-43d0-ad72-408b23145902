"""
ملف WSGI للإنتاج على PythonAnywhere
"""

import os
import sys

# إضافة مسار المشروع
path = '/home/<USER>/qurania'  # مسار المشروع الصحيح
if path not in sys.path:
    sys.path.insert(0, path)

# تعيين متغير البيئة لإعدادات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania.settings_production')

# استيراد تطبيق Django WSGI
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
