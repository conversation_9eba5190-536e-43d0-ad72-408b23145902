# متطلبات أكاديمية القرآنية للاستضافة المشتركة
# إصدارات محدودة ومتوافقة مع الاستضافة المشتركة

# Django Framework - إصدار مستقر
Django==4.2.7

# قاعدة البيانات MySQL
mysqlclient==2.2.0
# بديل إذا لم يعمل mysqlclient
PyMySQL==1.1.0

# API Framework
djangorestframework==3.14.0

# المصادقة والأمان
PyJWT==2.8.0

# HTTP Requests
requests==2.31.0

# معالجة التواريخ
pytz==2023.3

# معالجة الصور (إصدار محدود)
Pillow==10.0.1

# الدفع الإلكتروني
stripe==7.8.0

# الملفات الثابتة
whitenoise==6.6.0

# النماذج
django-crispy-forms==2.1

# CORS Headers
django-cors-headers==4.3.1

# إدارة المتغيرات
python-decouple==3.8

# تحسين الأداء
django-extensions==3.2.3

# إدارة الوقت
python-dateutil==2.8.2

# تشفير البيانات
cryptography==41.0.7

# معالجة JSON
jsonfield==3.1.0

# إدارة الملفات
django-storages==1.14.2

# تحسين قاعدة البيانات
django-debug-toolbar==4.2.0

# إدارة المهام البسيطة
django-crontab==0.7.1

# معالجة النصوص العربية
arabic-reshaper==3.0.0
python-bidi==0.4.2

# إنشاء PDF
reportlab==4.0.7

# معالجة Excel
openpyxl==3.1.2

# إدارة الإشعارات البسيطة
django-notifications-hq==1.8.3

# تحسين الاستعلامات
django-select2==8.1.2

# إدارة الصلاحيات
django-guardian==2.4.0

# معالجة الصور المتقدمة
django-imagekit==5.0.0

# إدارة الجلسات المحسنة
django-session-timeout==0.1.0

# تحسين الأمان
django-security==0.17.0

# إدارة الرسائل
django-messages-extends==0.6.2

# معالجة الملفات المضغوطة
zipfile36==0.1.3

# إدارة التخزين المؤقت البسيط
django-cachalot==2.6.1

# تحسين الأداء للاستضافة المشتركة
django-compressor==4.4

# إدارة الصور المصغرة
sorl-thumbnail==12.10.0

# معالجة البريد الإلكتروني
django-mailer==2.3.1

# إدارة المحتوى الديناميكي
django-ckeditor==6.7.0

# تحسين SEO
django-meta==2.4.0

# إدارة الروابط الدائمة
django-autoslug==1.9.9

# معالجة الألوان
webcolors==1.13

# إدارة الوقت المحسنة
django-timezone-field==6.1.0

# تحسين النماذج
django-widget-tweaks==1.5.0

# إدارة الترقيم
django-pagination==1.0.7

# معالجة الملفات النصية
chardet==5.2.0

# تحسين الاستعلامات
django-bulk-update==2.2.0

# إدارة الإعدادات
django-environ==0.11.2

# معالجة الـ HTML
beautifulsoup4==4.12.2

# إدارة الملفات المؤقتة
django-cleanup==8.0.0

# تحسين الأداء العام
django-silk==5.0.4

# معالجة الـ CSV
django-import-export==3.3.1

# إدارة النسخ الاحتياطية
django-dbbackup==4.0.2

# تحسين الذاكرة
memory-profiler==0.61.0

# إدارة السجلات المحسنة
django-log-viewer==1.1.7

# معالجة الملفات الكبيرة
django-chunked-upload==2.0.0

# تحسين الاستجابة
django-ratelimit==4.1.0

# إدارة الصحة العامة
django-health-check==3.17.0
