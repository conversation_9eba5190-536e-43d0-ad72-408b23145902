#!/usr/bin/env python
"""
سكريبت إعداد قاعدة البيانات للإنتاج على PythonAnywhere
"""

import os
import sys
import django

# إضافة مسار المشروع
sys.path.insert(0, '/home/<USER>/qurania')  # مسار المشروع الصحيح

# تعيين إعدادات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania.settings_production')

# تهيئة Django
django.setup()

from django.core.management import execute_from_command_line
from django.contrib.auth import get_user_model
from django.db import connection

def setup_database():
    """إعداد قاعدة البيانات الأولي"""
    
    print("🚀 بدء إعداد قاعدة البيانات...")
    
    # 1. تطبيق الهجرات
    print("📦 تطبيق هجرات قاعدة البيانات...")
    execute_from_command_line(['manage.py', 'makemigrations'])
    execute_from_command_line(['manage.py', 'migrate'])
    
    # 2. جمع الملفات الثابتة
    print("📁 جمع الملفات الثابتة...")
    execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
    
    # 3. إنشاء مستخدم مدير
    print("👤 إنشاء مستخدم المدير...")
    User = get_user_model()
    
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123456',  # غير كلمة المرور هذه!
            first_name='مدير',
            last_name='النظام',
            user_type='admin'
        )
        print(f"✅ تم إنشاء مستخدم المدير: {admin_user.username}")
    else:
        print("ℹ️ مستخدم المدير موجود بالفعل")
    
    # 4. إنشاء بيانات تجريبية (اختياري)
    create_sample_data()
    
    print("🎉 تم إعداد قاعدة البيانات بنجاح!")

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    
    print("📝 إنشاء بيانات تجريبية...")
    
    User = get_user_model()
    
    # إنشاء معلم تجريبي
    if not User.objects.filter(username='teacher1').exists():
        teacher = User.objects.create_user(
            username='teacher1',
            email='<EMAIL>',
            password='teacher123',
            first_name='أحمد',
            last_name='المعلم',
            user_type='teacher',
            phone='966501234567'
        )
        print(f"✅ تم إنشاء معلم تجريبي: {teacher.username}")
    
    # إنشاء طالب تجريبي
    if not User.objects.filter(username='student1').exists():
        student = User.objects.create_user(
            username='student1',
            email='<EMAIL>',
            password='student123',
            first_name='محمد',
            last_name='الطالب',
            user_type='student',
            phone='966507654321'
        )
        print(f"✅ تم إنشاء طالب تجريبي: {student.username}")

def check_database_connection():
    """فحص الاتصال بقاعدة البيانات"""
    
    print("🔍 فحص الاتصال بقاعدة البيانات...")
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                print("✅ الاتصال بقاعدة البيانات ناجح")
                return True
    except Exception as e:
        print(f"❌ فشل الاتصال بقاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("🏛️ إعداد أكاديمية القرآنية - PythonAnywhere")
    print("=" * 50)
    
    # فحص الاتصال بقاعدة البيانات
    if not check_database_connection():
        print("❌ يرجى التأكد من إعدادات قاعدة البيانات")
        return
    
    # إعداد قاعدة البيانات
    setup_database()
    
    print("\n" + "=" * 50)
    print("🎉 تم إعداد المشروع بنجاح!")
    print("=" * 50)
    print("\n📋 معلومات تسجيل الدخول:")
    print("👤 المدير:")
    print("   - اسم المستخدم: admin")
    print("   - كلمة المرور: admin123456")
    print("\n👨‍🏫 المعلم التجريبي:")
    print("   - اسم المستخدم: teacher1")
    print("   - كلمة المرور: teacher123")
    print("\n👨‍🎓 الطالب التجريبي:")
    print("   - اسم المستخدم: student1")
    print("   - كلمة المرور: student123")
    print("\n⚠️ تذكر تغيير كلمات المرور في الإنتاج!")

if __name__ == '__main__':
    main()
