from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth import get_user_model
from django.utils import timezone
from notifications.models import Notification
from .models import UserProfile
import json
import pytz
import logging

from lessons.models import LiveLesson
from lessons.quality_monitoring import QualityMonitoringService

User = get_user_model()
logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["GET"])
def notification_count(request):
    """إرجاع عدد الإشعارات غير المقروءة"""
    count = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).count()

    return JsonResponse({'count': count})


@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """تحديد إشعار كمقروء"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            recipient=request.user
        )
        notification.mark_as_read()
        return JsonResponse({'success': True})
    except Notification.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Notification not found'})


@login_required
@require_http_methods(["GET"])
def user_search(request):
    """البحث عن المستخدمين"""
    query = request.GET.get('q', '')
    user_type = request.GET.get('type', '')

    if len(query) < 2:
        return JsonResponse({'users': []})

    users = User.objects.filter(
        first_name__icontains=query
    ) | User.objects.filter(
        last_name__icontains=query
    ) | User.objects.filter(
        username__icontains=query
    )

    if user_type:
        users = users.filter(user_type=user_type)

    users = users[:10]  # حد أقصى 10 نتائج

    users_data = []
    for user in users:
        users_data.append({
            'id': user.id,
            'name': user.get_full_name(),
            'username': user.username,
            'user_type': user.get_user_type_display(),
            'email': user.email,
        })

    return JsonResponse({'users': users_data})


@login_required
@require_http_methods(["GET"])
def user_profile(request):
    """إرجاع بيانات الملف الشخصي للمستخدم"""
    user = request.user

    profile_data = {
        'id': user.id,
        'username': user.username,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'email': user.email,
        'phone': user.phone,
        'user_type': user.user_type,
        'user_type_display': user.get_user_type_display(),
        'date_joined': user.date_joined.isoformat(),
    }

    if hasattr(user, 'profile'):
        profile_data.update({
            'preferred_language': user.profile.preferred_language,
            'timezone': user.profile.timezone,
            'address': user.profile.address,
        })

    return JsonResponse({'profile': profile_data})


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def update_timezone(request):
    """تحديث المنطقة الزمنية للمستخدم"""
    try:
        data = json.loads(request.body)
        new_timezone = data.get('timezone')

        if not new_timezone:
            return JsonResponse({'success': False, 'error': 'Timezone is required'})

        # التحقق من صحة المنطقة الزمنية
        try:
            pytz.timezone(new_timezone)
        except pytz.exceptions.UnknownTimeZoneError:
            return JsonResponse({'success': False, 'error': 'Invalid timezone'})

        # الحصول على أو إنشاء ملف تعريف المستخدم
        profile, created = UserProfile.objects.get_or_create(user=request.user)
        profile.timezone = new_timezone
        profile.save()

        return JsonResponse({
            'success': True,
            'timezone': new_timezone,
            'message': 'تم تحديث المنطقة الزمنية بنجاح'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def get_time_elements(request):
    """إرجاع عناصر الوقت المحدثة حسب المنطقة الزمنية للمستخدم"""
    try:
        # الحصول على المنطقة الزمنية للمستخدم
        user_timezone = 'Asia/Riyadh'  # افتراضي
        if hasattr(request.user, 'profile') and request.user.profile.timezone:
            user_timezone = request.user.profile.timezone

        # تحويل الوقت الحالي للمنطقة الزمنية للمستخدم
        user_tz = pytz.timezone(user_timezone)
        current_time = timezone.now().astimezone(user_tz)

        # تنسيق الأوقات المختلفة
        time_elements = [
            {
                'selector': '#current-time',
                'content': current_time.strftime('%H:%M:%S')
            },
            {
                'selector': '#current-time-date',
                'content': current_time.strftime('%H:%M:%S %Y-%m-%d')
            },
            {
                'selector': '.current-date',
                'content': current_time.strftime('%Y-%m-%d')
            },
            {
                'selector': '.current-time-only',
                'content': current_time.strftime('%H:%M')
            }
        ]

        return JsonResponse({
            'success': True,
            'timezone': user_timezone,
            'timeElements': time_elements,
            'currentTime': current_time.isoformat()
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})





@login_required
@require_http_methods(["POST"])
def api_end_live_lesson(request, lesson_id):
    """API لإنهاء الحصة المباشرة"""
    try:
        from lessons.models import LiveLesson
        from django.shortcuts import get_object_or_404
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"User {request.user.id} attempting to end lesson {lesson_id}")

        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or request.user == live_lesson.teacher):
            logger.warning(f"User {request.user.id} denied access to end lesson {lesson_id}")
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لإنهاء هذه الحصة'})

        # التحقق من حالة الحصة
        if live_lesson.status != 'live':
            logger.warning(f"Lesson {lesson_id} status is {live_lesson.status}, not live")
            return JsonResponse({'success': False, 'message': f'هذه الحصة ليست جارية - الحالة الحالية: {live_lesson.get_status_display()}'})

        # فحص الحد الأدنى للمدة قبل الإنهاء
        from lessons.models import LessonQualitySettings
        settings = LessonQualitySettings.get_settings()

        if live_lesson.started_at:
            current_duration = (timezone.now() - live_lesson.started_at).total_seconds() / 60
            if current_duration < settings.minimum_duration_minutes and not request.user.is_admin():
                return JsonResponse({
                    'success': False,
                    'message': f'لا يمكن إنهاء الحصة قبل {settings.minimum_duration_minutes} دقيقة. المدة الحالية: {int(current_duration)} دقيقة',
                    'current_duration': int(current_duration),
                    'minimum_duration': settings.minimum_duration_minutes,
                    'remaining_minutes': settings.minimum_duration_minutes - int(current_duration)
                })

        # إنهاء الحصة
        try:
            live_lesson.end_lesson()
            logger.info(f"Lesson {lesson_id} ended successfully by user {request.user.id}")

            # إنهاء مراقبة الجودة وإنشاء التقرير
            quality_report = QualityMonitoringService.end_lesson_monitoring(
                live_lesson=live_lesson,
                user=request.user,
                request=request
            )

            response_data = {
                'success': True,
                'message': 'تم إنهاء الحصة بنجاح',
                'status': live_lesson.status
            }

            # إضافة معلومات الجودة إذا تم إنشاء التقرير
            if quality_report:
                response_data['quality_report'] = {
                    'duration_minutes': quality_report.actual_duration_minutes,
                    'quality_status': quality_report.quality_status,
                    'is_completed': quality_report.is_completed,
                    'is_suspicious': quality_report.is_suspicious
                }

            return JsonResponse(response_data)

        except Exception as end_error:
            logger.error(f"Error ending lesson {lesson_id}: {str(end_error)}")
            return JsonResponse({'success': False, 'message': f'خطأ في إنهاء الحصة: {str(end_error)}'})

    except LiveLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المباشرة غير موجودة'})
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error in api_end_live_lesson: {str(e)}")
        return JsonResponse({'success': False, 'message': f'حدث خطأ غير متوقع: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def api_auto_end_live_lesson(request, lesson_id):
    """API لإنهاء الحصة تلقائياً عند انتهاء المدة المحددة"""
    try:
        from lessons.models import LiveLesson
        from django.shortcuts import get_object_or_404
        from django.utils import timezone
        import json
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Auto-ending lesson {lesson_id} by user {request.user.id}")

        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            logger.warning(f"User {request.user.id} denied access to auto-end lesson {lesson_id}")
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لإنهاء هذه الحصة'})

        # التحقق من حالة الحصة
        if live_lesson.status != 'live':
            return JsonResponse({'success': False, 'message': 'الحصة ليست مباشرة حالياً'})

        # قراءة بيانات الطلب
        try:
            data = json.loads(request.body)
            reason = data.get('reason', 'timeout')
            actual_duration = data.get('actual_duration', 0)
        except:
            reason = 'timeout'
            actual_duration = 0

        # حساب المدة الفعلية إذا لم تُرسل
        if not actual_duration and live_lesson.started_at:
            elapsed_time = timezone.now() - live_lesson.started_at
            actual_duration = int(elapsed_time.total_seconds() / 60)

        # إنهاء الحصة
        live_lesson.status = 'ended'
        live_lesson.ended_at = timezone.now()
        live_lesson.save()

        # تسجيل سبب الإنهاء التلقائي
        logger.info(f"Lesson {lesson_id} auto-ended. Reason: {reason}, Duration: {actual_duration} minutes")

        # إرسال إشعارات
        try:
            from notifications.utils import NotificationService
            NotificationService.notify_lesson_auto_ended(live_lesson, reason, actual_duration)
        except Exception as notif_error:
            logger.warning(f"Failed to send auto-end notifications: {str(notif_error)}")

        return JsonResponse({
            'success': True,
            'message': 'تم إنهاء الحصة تلقائياً',
            'reason': reason,
            'actual_duration': actual_duration,
            'scheduled_duration': live_lesson.duration_minutes,
            'ended_at': live_lesson.ended_at.isoformat()
        })

    except LiveLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المباشرة غير موجودة'})
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error in api_auto_end_live_lesson: {str(e)}")
        return JsonResponse({'success': False, 'message': f'حدث خطأ غير متوقع: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def api_duration_update(request, lesson_id):
    """API لتحديث حالة مدة الحصة"""
    try:
        from lessons.models import LiveLesson
        from django.shortcuts import get_object_or_404
        import json
        import logging

        logger = logging.getLogger(__name__)
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})

        # قراءة بيانات التحديث
        try:
            data = json.loads(request.body)
            status = data.get('status')
            duration_minutes = data.get('duration_minutes')
            actual_duration = data.get('actual_duration', 0)
            timestamp = data.get('timestamp')
        except:
            return JsonResponse({'success': False, 'message': 'بيانات غير صحيحة'})

        # تسجيل التحديث
        logger.info(f"Duration update for lesson {lesson_id}: {status}, {actual_duration}/{duration_minutes} minutes")

        # معالجة التحديثات المختلفة
        if status == 'warning':
            warning_type = data.get('type', 'unknown')
            minutes_left = data.get('minutesLeft', 0)
            logger.info(f"Duration warning for lesson {lesson_id}: {warning_type}, {minutes_left} minutes left")

        elif status == 'ended':
            reason = data.get('reason', 'timeout')
            logger.info(f"Lesson {lesson_id} duration ended: {reason}")

        return JsonResponse({
            'success': True,
            'message': 'تم تحديث حالة المدة',
            'status': status
        })

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in api_duration_update: {str(e)}")
        return JsonResponse({'success': False, 'message': f'خطأ في التحديث: {str(e)}'})


@login_required
@require_http_methods(["GET"])
def api_live_lesson_status(request, lesson_id):
    """API للحصول على حالة الحصة المباشرة"""
    from lessons.models import LiveLesson
    from django.shortcuts import get_object_or_404

    try:
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الحصة'})

        # التحقق من ضرورة إنهاء الحصة تلقائياً
        auto_ended = live_lesson.auto_end_if_needed()

        response_data = {
            'success': True,
            'status': live_lesson.status,
            'started_at': live_lesson.started_at.isoformat() if live_lesson.started_at else None,
            'ended_at': live_lesson.ended_at.isoformat() if live_lesson.ended_at else None,
            'auto_ended': auto_ended
        }

        if auto_ended:
            response_data['message'] = 'تم إنهاء الحصة تلقائياً بعد انقضاء المدة المحددة'

        return JsonResponse(response_data)

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_admin_live_lessons_status(request):
    """API للمدير لمراقبة حالة جميع الحصص المباشرة"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه البيانات'})

    from lessons.models import LiveLesson

    try:
        # جلب جميع الحصص المباشرة الجارية
        live_lessons = LiveLesson.objects.filter(status='live').select_related('teacher', 'student')

        lessons_data = []
        auto_ended_count = 0

        for lesson in live_lessons:
            # التحقق من ضرورة إنهاء الحصة تلقائياً
            auto_ended = lesson.auto_end_if_needed()
            if auto_ended:
                auto_ended_count += 1

            lessons_data.append({
                'id': lesson.id,
                'title': lesson.title,
                'teacher': lesson.teacher.get_full_name(),
                'student': lesson.student.get_full_name(),
                'status': lesson.status,
                'started_at': lesson.started_at.isoformat() if lesson.started_at else None,
                'duration_minutes': lesson.duration_minutes,
                'auto_ended': auto_ended
            })

        return JsonResponse({
            'success': True,
            'lessons': lessons_data,
            'auto_ended_count': auto_ended_count,
            'total_live_lessons': len([l for l in lessons_data if l['status'] == 'live'])
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_live_lessons_sidebar(request):
    """API للحصول على الحصص المباشرة للقائمة الجانبية"""
    from lessons.models import LiveLesson
    from django.utils import timezone

    try:
        live_lessons_data = []
        scheduled_lessons_data = []

        if request.user.is_teacher():
            now = timezone.now()
            today_end = now.replace(hour=23, minute=59, second=59, microsecond=999999)

            # الحصص المباشرة الجارية للمعلم - فقط المخصصة له
            live_lessons = LiveLesson.objects.filter(
                teacher=request.user,
                status='live'
            ).select_related('student').order_by('-started_at')

            # الحصص المباشرة المجدولة للمعلم - فقط الجارية والقادمة خلال اليوم
            scheduled_live_lessons = LiveLesson.objects.filter(
                teacher=request.user,
                status='scheduled',
                scheduled_date__gte=now,  # من الآن فصاعداً
                scheduled_date__lte=today_end  # حتى نهاية اليوم
            ).select_related('student').order_by('scheduled_date')[:3]

            # الحصص المجدولة من نظام الاشتراكات - فقط التي تم تعيين المعلم لها (ليس المكتملة)
            from subscriptions.models import ScheduledLesson
            subscription_lessons = ScheduledLesson.objects.filter(
                teacher=request.user,  # المعلم المحدد لهذه الحصص
                subscription__status='active',  # الاشتراكات النشطة فقط
                status='scheduled',  # فقط المجدولة (ليس المكتملة)
                scheduled_date__gte=now,  # من الآن
                scheduled_date__lte=now + timezone.timedelta(minutes=30)  # خلال الـ 30 دقيقة القادمة
            ).select_related(
                'subscription__student',
                'subscription__plan'
            ).order_by('scheduled_date')[:2]

            # تحويل البيانات للـ JSON
            for lesson in live_lessons:
                live_lessons_data.append({
                    'id': lesson.id,
                    'title': lesson.title,
                    'student_name': lesson.student.get_full_name(),
                    'started_at': lesson.started_at.isoformat() if lesson.started_at else None,
                    'type': 'live'
                })

            for lesson in scheduled_live_lessons:
                scheduled_lessons_data.append({
                    'id': lesson.id,
                    'title': lesson.title,
                    'student_name': lesson.student.get_full_name(),
                    'scheduled_date': lesson.scheduled_date.isoformat(),
                    'type': 'live_scheduled'
                })

            # إضافة الحصص المجدولة من الاشتراكات
            for lesson in subscription_lessons:
                scheduled_lessons_data.append({
                    'id': lesson.id,
                    'title': f'حصة رقم {lesson.lesson_number}',
                    'student_name': lesson.subscription.student.get_full_name(),
                    'scheduled_date': lesson.scheduled_date.isoformat(),
                    'plan_name': lesson.subscription.plan.name,
                    'type': 'subscription_scheduled'
                })

        elif request.user.is_student():
            # الحصص المباشرة الجارية للطالب
            live_lessons = LiveLesson.objects.filter(
                student=request.user,
                status='live'
            ).select_related('teacher').order_by('-started_at')

            # الحصص المجدولة للطالب
            now = timezone.now()
            scheduled_live_lessons = LiveLesson.objects.filter(
                student=request.user,
                status='scheduled',
                scheduled_date__gte=now - timezone.timedelta(hours=2)
            ).select_related('teacher').order_by('scheduled_date')[:3]

            # تحويل البيانات للـ JSON
            for lesson in live_lessons:
                live_lessons_data.append({
                    'id': lesson.id,
                    'title': lesson.title,
                    'teacher_name': lesson.teacher.get_full_name(),
                    'started_at': lesson.started_at.isoformat() if lesson.started_at else None
                })

            for lesson in scheduled_live_lessons:
                scheduled_lessons_data.append({
                    'id': lesson.id,
                    'title': lesson.title,
                    'teacher_name': lesson.teacher.get_full_name(),
                    'scheduled_date': lesson.scheduled_date.isoformat()
                })

        return JsonResponse({
            'success': True,
            'live_lessons': live_lessons_data,
            'scheduled_live_lessons': scheduled_lessons_data
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_rate_live_lesson(request, lesson_id):
    """API لتقييم الحصة المباشرة"""
    from lessons.models import LiveLesson, LiveLessonRating
    from django.shortcuts import get_object_or_404
    import json

    try:
        # التحقق من أن المستخدم طالب
        if not request.user.is_student():
            return JsonResponse({'success': False, 'message': 'فقط الطلاب يمكنهم تقييم الحصص'})

        # جلب الحصة المباشرة
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من أن الطالب مشارك في الحصة
        if live_lesson.student != request.user:
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لتقييم هذه الحصة'})

        # التحقق من أن الحصة انتهت
        if live_lesson.status != 'ended':
            return JsonResponse({'success': False, 'message': 'لا يمكن تقييم حصة لم تنته بعد'})

        # التحقق من عدم وجود تقييم سابق
        if hasattr(live_lesson, 'rating'):
            return JsonResponse({'success': False, 'message': 'تم تقييم هذه الحصة مسبقاً'})

        # قراءة البيانات
        data = json.loads(request.body)

        # التحقق من صحة البيانات
        required_fields = ['overall_rating', 'lesson_quality', 'teacher_interaction', 'technical_quality']
        for field in required_fields:
            if field not in data or not isinstance(data[field], int) or data[field] < 1 or data[field] > 5:
                return JsonResponse({'success': False, 'message': f'قيمة غير صحيحة للحقل {field}'})

        # إنشاء التقييم
        rating = LiveLessonRating.objects.create(
            live_lesson=live_lesson,
            student=request.user,
            teacher=live_lesson.teacher,
            overall_rating=data['overall_rating'],
            lesson_quality=data['lesson_quality'],
            teacher_interaction=data['teacher_interaction'],
            technical_quality=data['technical_quality'],
            comment=data.get('comment', '')
        )

        return JsonResponse({
            'success': True,
            'message': 'تم حفظ التقييم بنجاح',
            'rating_id': rating.id,
            'average_rating': rating.average_rating
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'بيانات غير صحيحة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_admin_live_lessons_realtime(request):
    """API للحصول على حالة الحصص المباشرة في الوقت الفعلي مع التصنيف"""
    if not request.user.is_admin():
        return JsonResponse({'success': False, 'message': 'غير مصرح'})

    try:
        from lessons.models import LiveLesson
        from django.utils import timezone

        now = timezone.now()

        # الحصص الجارية الآن
        live_lessons = LiveLesson.objects.filter(
            status='live'
        ).select_related('teacher', 'student').order_by('-started_at')

        # الحصص المجدولة (أكثر من ساعة من الآن)
        scheduled_lessons = LiveLesson.objects.filter(
            status='scheduled',
            scheduled_date__gt=now + timezone.timedelta(hours=1)
        ).select_related('teacher', 'student').order_by('scheduled_date')

        # الحصص القادمة قريباً (خلال الساعة القادمة)
        upcoming_soon = LiveLesson.objects.filter(
            status='scheduled',
            scheduled_date__gte=now - timezone.timedelta(minutes=15),
            scheduled_date__lte=now + timezone.timedelta(hours=1)
        ).select_related('teacher', 'student').order_by('scheduled_date')

        # تحويل البيانات إلى JSON
        def lesson_to_dict(lesson):
            return {
                'id': lesson.id,
                'title': lesson.title,
                'student_name': lesson.student.get_full_name(),
                'teacher_name': lesson.teacher.get_full_name(),
                'duration_minutes': lesson.duration_minutes,
                'scheduled_date': lesson.scheduled_date.isoformat(),
                'started_at': lesson.started_at.isoformat() if lesson.started_at else None,
                'status': lesson.status,
                'jitsi_url': lesson.get_jitsi_url(),
            }

        return JsonResponse({
            'success': True,
            'live_lessons': [lesson_to_dict(lesson) for lesson in live_lessons],
            'scheduled_lessons': [lesson_to_dict(lesson) for lesson in scheduled_lessons],
            'upcoming_soon': [lesson_to_dict(lesson) for lesson in upcoming_soon],
            'live_count': live_lessons.count(),
            'scheduled_count': scheduled_lessons.count(),
            'upcoming_soon_count': upcoming_soon.count(),
            'timestamp': now.isoformat(),
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_start_live_lesson(request, lesson_id):
    """API لبدء حصة مباشرة"""
    try:
        from lessons.models import LiveLesson
        from django.utils import timezone

        lesson = LiveLesson.objects.get(id=lesson_id)

        # التحقق من الصلاحيات - المدير أو المعلم المسؤول عن الحصة
        if not (request.user.is_admin() or request.user == lesson.teacher):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لبدء هذه الحصة'})

        if lesson.status != 'scheduled':
            return JsonResponse({'success': False, 'message': 'لا يمكن بدء هذه الحصة - الحالة الحالية: ' + lesson.get_status_display()})

        # التحقق من التوقيت - لا يمكن بدء الحصة إلا قبل موعدها بـ 5 دقائق فقط
        if lesson.scheduled_date and request.user.is_teacher():  # المدير يمكنه البدء في أي وقت
            now = timezone.now()
            time_diff = (lesson.scheduled_date - now).total_seconds() / 60  # بالدقائق

            if time_diff > 5:
                return JsonResponse({
                    'success': False,
                    'message': f'لا يمكن بدء الحصة قبل {int(time_diff)} دقيقة من موعدها. يمكنك البدء قبل 5 دقائق فقط من الموعد المحدد.'
                })

        # تحديث حالة الحصة
        lesson.status = 'live'
        lesson.started_at = timezone.now()
        lesson.save()

        # بدء مراقبة الجودة
        quality_report = QualityMonitoringService.start_lesson_monitoring(
            live_lesson=lesson,
            teacher=lesson.teacher,
            request=request
        )

        # إرسال إشعارات
        try:
            from notifications.utils import NotificationService
            NotificationService.notify_live_lesson_started(lesson)
        except Exception as notification_error:
            # لا نريد أن يفشل بدء الحصة بسبب خطأ في الإشعارات
            print(f"خطأ في إرسال الإشعارات: {notification_error}")

        response_data = {
            'success': True,
            'message': 'تم بدء الحصة بنجاح',
            'lesson_id': lesson.id,
            'status': lesson.status,
            'started_at': lesson.started_at.isoformat(),
            'live_lesson_url': f'/dashboard/teacher/live-lesson/{lesson.id}/'
        }

        # إضافة معلومات مراقبة الجودة
        if quality_report:
            response_data['quality_monitoring'] = {
                'enabled': True,
                'report_id': quality_report.id
            }

        return JsonResponse(response_data)

    except LiveLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_get_subscription_plan_id(request, subscription_id):
    """API للحصول على plan_id من الاشتراك للتجديد"""
    try:
        from subscriptions.models import StudentSubscription

        subscription = StudentSubscription.objects.get(
            id=subscription_id,
            student=request.user
        )

        return JsonResponse({
            'success': True,
            'plan_id': subscription.plan.id,
            'plan_name': subscription.plan.name
        })

    except StudentSubscription.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الاشتراك غير موجود'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_start_scheduled_lesson(request, lesson_id):
    """API لبدء الحصة المجدولة وتحويلها إلى حصة مباشرة"""
    try:
        from subscriptions.models import ScheduledLesson
        from django.shortcuts import get_object_or_404

        # جلب الحصة المجدولة
        scheduled_lesson = get_object_or_404(ScheduledLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or request.user == scheduled_lesson.teacher):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية لبدء هذه الحصة'})

        # التحقق من إمكانية البدء
        can_start, message = scheduled_lesson.can_start_now(request.user)
        if not can_start:
            return JsonResponse({'success': False, 'message': message})

        # الحصول على أو إنشاء الحصة المباشرة
        live_lesson = scheduled_lesson.get_or_create_live_lesson(request.user)

        # بدء الحصة المباشرة
        from django.utils import timezone
        live_lesson.status = 'live'
        live_lesson.started_at = timezone.now()
        live_lesson.save()

        # إرسال إشعارات
        try:
            from notifications.utils import NotificationService
            NotificationService.notify_live_lesson_started(live_lesson)
        except Exception as notification_error:
            print(f"خطأ في إرسال الإشعارات: {notification_error}")

        return JsonResponse({
            'success': True,
            'message': 'تم بدء الحصة بنجاح',
            'live_lesson_url': f'/dashboard/teacher/live-lesson/{live_lesson.id}/',
            'live_lesson_id': live_lesson.id,
            'scheduled_lesson_id': scheduled_lesson.id
        })

    except ScheduledLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المجدولة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_join_scheduled_lesson(request, lesson_id):
    """API للطلاب للانضمام للحصة المجدولة"""
    try:
        from subscriptions.models import ScheduledLesson
        from django.shortcuts import get_object_or_404

        # جلب الحصة المجدولة
        scheduled_lesson = get_object_or_404(ScheduledLesson, id=lesson_id)

        # التحقق من أن الطالب هو صاحب الحصة
        if not (request.user.is_admin() or request.user == scheduled_lesson.subscription.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للانضمام لهذه الحصة'})

        # التحقق من وجود الحصة المباشرة
        if not scheduled_lesson.live_lesson_id:
            return JsonResponse({'success': False, 'message': 'لم يتم بدء الحصة بعد. يرجى انتظار المعلم لبدء الحصة.'})

        # جلب الحصة المباشرة
        from lessons.models import LiveLesson
        try:
            live_lesson = LiveLesson.objects.get(id=scheduled_lesson.live_lesson_id)
        except LiveLesson.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'الحصة المباشرة غير موجودة'})

        # التحقق من حالة الحصة
        if live_lesson.status not in ['scheduled', 'live']:
            return JsonResponse({'success': False, 'message': f'الحصة في حالة: {live_lesson.get_status_display()}'})

        return JsonResponse({
            'success': True,
            'message': 'يمكنك الانضمام للحصة الآن',
            'live_lesson_url': f'/dashboard/student/live-lesson/{live_lesson.id}/',
            'live_lesson_id': live_lesson.id,
            'scheduled_lesson_id': scheduled_lesson.id
        })

    except ScheduledLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المجدولة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["GET"])
def api_live_lesson_participants(request, lesson_id):
    """API للحصول على عدد المشاركين في الحصة المباشرة"""
    try:
        from lessons.models import LiveLesson, LiveLessonAttendance
        from django.shortcuts import get_object_or_404

        # جلب الحصة المباشرة
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الحصة'})

        # حساب عدد المشاركين النشطين
        active_participants = LiveLessonAttendance.objects.filter(
            live_lesson=live_lesson,
            left_at__isnull=True  # لم يغادروا بعد
        ).count()

        return JsonResponse({
            'success': True,
            'count': active_participants,
            'lesson_id': live_lesson.id,
            'lesson_status': live_lesson.status
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_leave_live_lesson(request, lesson_id):
    """API لتسجيل خروج المستخدم من الحصة المباشرة"""
    try:
        from lessons.models import LiveLesson
        from django.shortcuts import get_object_or_404
        from django.utils import timezone

        # جلب الحصة المباشرة
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الحصة'})

        # تسجيل خروج المستخدم
        try:
            # البحث عن سجل الحضور الحالي
            from lessons.models import LiveLessonAttendance
            attendance = LiveLessonAttendance.objects.filter(
                live_lesson=live_lesson,
                user=request.user,
                left_at__isnull=True  # لم يسجل خروج بعد
            ).first()

            if attendance:
                # تسجيل وقت الخروج
                attendance.left_at = timezone.now()
                attendance.calculate_duration()
                attendance.save()

                # إذا كان الطالب هو من خرج، تحقق من إكمال الحصة
                if request.user == live_lesson.student:
                    # إكمال الحصة المجدولة المرتبطة
                    live_lesson._complete_related_scheduled_lesson()

                return JsonResponse({
                    'success': True,
                    'message': 'تم تسجيل خروجك من الحصة',
                    'duration_minutes': attendance.duration_minutes
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'لم يتم العثور على سجل حضور نشط'
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'خطأ في تسجيل الخروج: {str(e)}'
            })

    except LiveLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المباشرة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_join_live_lesson(request, lesson_id):
    """API لتسجيل دخول المستخدم للحصة المباشرة"""
    try:
        from lessons.models import LiveLesson
        from django.shortcuts import get_object_or_404

        # جلب الحصة المباشرة
        live_lesson = get_object_or_404(LiveLesson, id=lesson_id)

        # التحقق من الصلاحيات
        if not (request.user.is_admin() or
                request.user == live_lesson.teacher or
                request.user == live_lesson.student):
            return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية للوصول إلى هذه الحصة'})

        # تسجيل دخول المستخدم
        live_lesson.record_user_join(request.user)

        return JsonResponse({
            'success': True,
            'message': 'تم تسجيل دخولك للحصة بنجاح',
            'lesson_id': live_lesson.id,
            'lesson_title': live_lesson.title
        })

    except LiveLesson.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'الحصة المباشرة غير موجودة'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)})


@login_required
@require_http_methods(["POST"])
def api_teacher_lesson_report(request):
    """API لحفظ تقرير المعلم عن الحصة"""
    if not request.user.is_teacher():
        return JsonResponse({'success': False, 'message': 'ليس لديك صلاحية'})

    try:
        from lessons.models import LiveLesson, TeacherLessonReport
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Teacher {request.user.id} submitting lesson report")
        logger.info(f"POST data: {request.POST}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Content type: {request.content_type}")
        logger.info(f"User authenticated: {request.user.is_authenticated}")
        logger.info(f"User type: {request.user.user_type if hasattr(request.user, 'user_type') else 'No user_type'}")

        lesson_id = request.POST.get('lesson_id')
        if not lesson_id:
            return JsonResponse({'success': False, 'message': 'معرف الحصة مطلوب'})

        try:
            lesson = LiveLesson.objects.get(id=lesson_id, teacher=request.user)
        except LiveLesson.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'الحصة غير موجودة أو ليس لديك صلاحية للوصول إليها'})

        # التحقق من عدم وجود تقرير مسبق
        if hasattr(lesson, 'teacher_report'):
            return JsonResponse({'success': False, 'message': 'تم إنشاء تقرير لهذه الحصة مسبقاً'})

        # التحقق من البيانات المطلوبة
        required_fields = ['student_performance', 'student_participation', 'student_understanding', 'overall_lesson_rating']
        for field in required_fields:
            if not request.POST.get(field):
                return JsonResponse({'success': False, 'message': f'حقل {field} مطلوب'})

        # التحقق من النصوص المطلوبة
        required_text_fields = ['strengths', 'areas_for_improvement', 'lesson_summary']
        for field in required_text_fields:
            if not request.POST.get(field) or not request.POST.get(field).strip():
                return JsonResponse({'success': False, 'message': f'حقل {field} مطلوب'})

        # إنشاء التقرير
        report = TeacherLessonReport.objects.create(
            lesson=lesson,
            teacher=request.user,
            student=lesson.student,
            student_performance=int(request.POST.get('student_performance')),
            student_participation=int(request.POST.get('student_participation')),
            student_understanding=int(request.POST.get('student_understanding')),
            strengths=request.POST.get('strengths').strip(),
            areas_for_improvement=request.POST.get('areas_for_improvement').strip(),
            lesson_summary=request.POST.get('lesson_summary').strip(),
            homework_assigned=request.POST.get('homework_assigned', '').strip(),
            recommendations=request.POST.get('recommendations', '').strip(),
            overall_lesson_rating=int(request.POST.get('overall_lesson_rating')),
            additional_notes=request.POST.get('additional_notes', '').strip()
        )

        logger.info(f"Report created successfully with ID: {report.id}")

        # إرسال إشعار للمدير
        try:
            from notifications.models import Notification
            from django.contrib.auth import get_user_model

            User = get_user_model()
            admins = User.objects.filter(user_type='admin')

            for admin in admins:
                Notification.objects.create(
                    recipient=admin,
                    title='تقرير حصة جديد',
                    message=f'قام المعلم {request.user.get_full_name()} بإرسال تقرير للحصة "{lesson.title}" مع الطالب {lesson.student.get_full_name()}',
                    notification_type='lesson_report'
                )
        except Exception as e:
            logger.warning(f"Failed to send notification to admin: {str(e)}")

        return JsonResponse({'success': True, 'message': 'تم حفظ التقرير بنجاح وإرساله للمدير'})

    except ValueError as e:
        return JsonResponse({'success': False, 'message': 'قيم التقييم يجب أن تكون أرقام صحيحة'})
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in api_teacher_lesson_report: {str(e)}")
        return JsonResponse({'success': False, 'message': f'حدث خطأ: {str(e)}'})


@login_required
@require_http_methods(["GET"])
def api_payment_gateway_status(request):
    """API للحصول على حالة بوابات الدفع الحالية"""
    from users.payment_service import payment_service
    from users.models import PaymentGatewaySettings
    from django.utils import timezone

    try:
        # تحديث البيانات من قاعدة البيانات
        payment_service.clear_cache()

        # الحصول على الإعدادات مباشرة من قاعدة البيانات
        try:
            settings = PaymentGatewaySettings.objects.get(pk=1)
            # إعادة تحميل البيانات من قاعدة البيانات للتأكد من الحداثة
            settings.refresh_from_db()
        except PaymentGatewaySettings.DoesNotExist:
            settings = PaymentGatewaySettings.objects.create(
                pk=1,
                paypal_enabled=False,
                stripe_enabled=False,
                bank_transfer_enabled=True
            )

        return JsonResponse({
            'success': True,
            'payment_methods': {
                'paypal': {
                    'enabled': payment_service.is_paypal_enabled(),
                    'name': 'PayPal',
                    'icon': 'fab fa-paypal',
                    'color': 'blue'
                },
                'stripe': {
                    'enabled': payment_service.is_stripe_enabled(),
                    'name': 'Stripe',
                    'icon': 'fab fa-stripe',
                    'color': 'purple'
                },
                'bank_transfer': {
                    'enabled': payment_service.is_bank_transfer_enabled(),
                    'name': 'التحويل البنكي',
                    'icon': 'fas fa-university',
                    'color': 'green'
                }
            },
            'raw_settings': {
                'paypal_enabled': settings.paypal_enabled,
                'stripe_enabled': settings.stripe_enabled,
                'bank_transfer_enabled': settings.bank_transfer_enabled
            },
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e),
            'payment_methods': {
                'paypal': {'enabled': False, 'name': 'PayPal', 'icon': 'fab fa-paypal', 'color': 'blue'},
                'stripe': {'enabled': False, 'name': 'Stripe', 'icon': 'fab fa-stripe', 'color': 'purple'},
                'bank_transfer': {'enabled': False, 'name': 'التحويل البنكي', 'icon': 'fas fa-university', 'color': 'green'}
            }
        })