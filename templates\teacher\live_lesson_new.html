{% extends 'base.html' %}
{% load static %}

{% block title %}{{ live_lesson.title }} - حصة مباشرة (معلم){% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/jitsi-monitoring.css' %}">
<style>
    .lesson-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .lesson-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 24px;
        border-radius: 12px;
        margin-bottom: 24px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .status-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .jitsi-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .jitsi-container {
        width: 100%;
        height: 700px; /* زيادة الارتفاع من 500px إلى 700px */
        min-height: 700px; /* ضمان الحد الأدنى للارتفاع */
        border-radius: 8px;
        overflow: hidden;
        background: #000;
        border: 2px solid #e5e7eb;
        position: relative; /* لضمان عرض أزرار التحكم */
    }

    /* ضمان أن iframe داخل Jitsi يستخدم الارتفاع الكامل */
    .jitsi-container iframe,
    #teacher-jitsi-container iframe,
    #jitsi-container iframe {
        width: 100% !important;
        height: 700px !important;
        min-height: 700px !important;
        border: none !important;
    }

    .controls-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .sidebar {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .sidebar-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }

    .sidebar-link {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        color: #6b7280;
        text-decoration: none;
        transition: all 0.2s;
        font-size: 14px;
    }

    .sidebar-link:hover {
        background: #f3f4f6;
        color: #374151;
        transform: translateX(4px);
    }

    .quality-card {
        background: #f9fafb;
        padding: 12px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid #e5e7eb;
    }

    .instructions-card {
        background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        border: 1px solid #10b981;
        border-radius: 12px;
        padding: 20px;
        margin-top: 20px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }

    .btn-primary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }

    .btn-secondary:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }

    .btn-danger {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }

    .btn-danger:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
    }
    
    .status-indicator {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .monitoring-panel {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 2px solid #0ea5e9;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .teacher-panel {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 2px solid #f59e0b;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .student-panel {
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        border: 2px solid #10b981;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
    
    .quality-card {
        background: white;
        border-radius: 8px;
        padding: 16px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    
    .quality-card:hover {
        transform: translateY(-2px);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-primary:hover:not(:disabled) {
        background: linear-gradient(135deg, #047857 0%, #059669 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .btn-secondary {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .btn-warning {
        background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="lesson-container">
    <!-- معلومات الحصة -->
    <div class="lesson-info">
        <h1 class="text-2xl font-bold mb-2">{{ live_lesson.title }}</h1>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <i class="fas fa-user-graduate ml-1"></i>
                <strong>الطالب:</strong> {{ live_lesson.student.get_full_name }}
            </div>
            <div>
                <i class="fas fa-calendar ml-1"></i>
                <strong>التاريخ:</strong> {{ live_lesson.scheduled_date|date:"Y/m/d" }}
            </div>
            <div>
                <i class="fas fa-clock ml-1"></i>
                <strong>الوقت:</strong> {{ live_lesson.scheduled_date|date:"H:i" }}
            </div>
        </div>
    </div>

    {% if live_lesson.status == 'scheduled' %}
    <!-- حالة الانتظار -->
    <div class="bg-white rounded-lg shadow-lg p-8 text-center">
        <i class="fas fa-play-circle text-6xl text-blue-400 mb-4"></i>
        <h3 class="text-xl font-bold text-gray-800 mb-2">جاهز لبدء الحصة</h3>
        <p class="text-gray-600 mb-4">اضغط على "بدء الحصة" لبدء الحصة المباشرة</p>
        <p class="text-sm text-gray-500">
            <i class="fas fa-info-circle ml-1"></i>
            سيتم إشعار الطالب تلقائياً عند بدء الحصة
        </p>
    </div>
    
    {% else %}
    <!-- تصميم مثل صفحة الطالب مع صلاحيات المعلم -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- حاوي Jitsi الرئيسي -->
        <div class="lg:col-span-2">
            <!-- حالة الحصة -->
            <div class="status-card">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div id="teacher-status-indicator" class="w-3 h-3 rounded-full bg-yellow-500 ml-3 animate-pulse"></div>
                        <span id="teacher-status-text" class="font-medium">جاهز للبدء</span>
                    </div>
                    <div id="teacher-timer" class="text-sm text-gray-600">
                        <i class="fas fa-stopwatch ml-1"></i>
                        <span id="teacher-timer-display">00:00:00</span>
                    </div>
                </div>
            </div>

            <!-- حاوي Jitsi -->
            <div class="jitsi-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-bold">
                        <i class="fas fa-video text-blue-600 ml-2"></i>
                        غرفة الحصة المباشرة
                    </h3>
                    <button id="teacher-start-jitsi" class="btn-primary">
                        <i class="fas fa-play ml-2"></i>
                        بدء الحصة
                    </button>
                </div>

                <!-- إطار Jitsi -->
                <div id="teacher-jitsi-container" class="jitsi-container"></div>
            </div>

            <!-- أزرار التحكم -->
            <div class="controls-card">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="teacher-confirm-attendance" class="btn-secondary" disabled>
                        <i class="fas fa-check-circle ml-2"></i>
                        تأكيد الحضور
                    </button>
                    <button id="teacher-end-lesson" class="btn-danger" disabled>
                        <i class="fas fa-sign-out-alt ml-2"></i>
                        إنهاء الحصة
                    </button>
                </div>
            </div>
        </div>

        <!-- لوحة جانبية للمعلم -->
        <div class="sidebar">
            <!-- معلومات المعلم -->
            <div class="sidebar-card">
                <h4 class="font-bold text-gray-800 mb-3">
                    <i class="fas fa-user-tie text-blue-600 ml-2"></i>
                    معلومات المعلم
                </h4>
                <div class="space-y-2 text-sm">
                    <div>
                        <i class="fas fa-user ml-1"></i>
                        <strong>الاسم:</strong> {{ request.user.get_full_name }}
                    </div>
                    <div>
                        <i class="fas fa-envelope ml-1"></i>
                        <strong>البريد:</strong> {{ request.user.email }}
                    </div>
                    <div>
                        <i class="fas fa-id-badge ml-1"></i>
                        <strong>الدور:</strong> معلم
                    </div>
                </div>
            </div>

            <!-- إحصائيات الحصة -->
            <div class="sidebar-card">
                <h4 class="font-bold text-gray-800 mb-3">
                    <i class="fas fa-chart-line text-green-600 ml-2"></i>
                    إحصائيات الحصة
                </h4>
                <div class="grid grid-cols-2 gap-3">
                    <div class="quality-card">
                        <i class="fas fa-heartbeat text-red-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-600">النبضات</p>
                        <p id="teacher-heartbeat-count" class="font-bold">0</p>
                    </div>
                    <div class="quality-card">
                        <i class="fas fa-star text-yellow-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-600">الجودة</p>
                        <p id="teacher-quality-score" class="font-bold">100</p>
                    </div>
                </div>
            </div>

            <!-- معلومات الطالب -->
            <div class="sidebar-card">
                <h4 class="font-bold text-gray-800 mb-3">
                    <i class="fas fa-user-graduate text-green-600 ml-2"></i>
                    معلومات الطالب
                </h4>
                <div class="space-y-2 text-sm">
                    <div>
                        <i class="fas fa-user ml-1"></i>
                        <strong>الاسم:</strong> {{ live_lesson.student.get_full_name }}
                    </div>
                    <div>
                        <i class="fas fa-envelope ml-1"></i>
                        <strong>البريد:</strong> {{ live_lesson.student.email }}
                    </div>
                    <div id="student-status-info">
                        <i class="fas fa-circle text-gray-400 ml-1"></i>
                        <strong>الحالة:</strong> <span id="student-status-display">لم ينضم بعد</span>
                    </div>
                </div>
            </div>

            <!-- أدوات المعلم -->
            <div class="sidebar-card">
                <h4 class="font-bold text-gray-800 mb-3">
                    <i class="fas fa-tools text-purple-600 ml-2"></i>
                    أدوات المعلم
                </h4>
                <div class="space-y-2">
                    <a href="{% url 'teacher_schedule' %}" class="sidebar-link">
                        <i class="fas fa-calendar ml-2"></i>
                        جدول الحصص
                    </a>
                    <a href="{% url 'teacher_students' %}" class="sidebar-link">
                        <i class="fas fa-users ml-2"></i>
                        الطلاب
                    </a>
                    <a href="{% url 'teacher_ratings' %}" class="sidebar-link">
                        <i class="fas fa-star ml-2"></i>
                        التقييمات
                    </a>
                    <a href="{% url 'admin_monitoring_dashboard' %}" class="sidebar-link">
                        <i class="fas fa-shield-alt ml-2"></i>
                        مراقبة الجودة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- تعليمات للمعلم -->
    <div class="instructions-card">
        <h5 class="font-bold text-green-800 mb-3">
            <i class="fas fa-info-circle ml-2"></i>
            تعليمات للمعلم
        </h5>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-700">
            <div>
                <h6 class="font-semibold mb-2">بدء الحصة:</h6>
                <ul class="space-y-1">
                    <li>• اضغط "بدء الحصة" لتشغيل Jitsi</li>
                    <li>• فعل الكاميرا والمايك</li>
                    <li>• اضغط "تأكيد الحضور"</li>
                </ul>
            </div>
            <div>
                <h6 class="font-semibold mb-2">أثناء الحصة:</h6>
                <ul class="space-y-1">
                    <li>• راقب حالة الطالب من اللوحة الجانبية</li>
                    <li>• تأكد من حضور الطالب 15 دقيقة على الأقل</li>
                    <li>• ابق الصفحة مفتوحة للمراقبة</li>
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% csrf_token %}

<script>
// تمرير معرف الحصة ومعلومات المستخدم للـ JavaScript
window.lessonId = {{ live_lesson.id }};
window.userRole = 'teacher';
window.userEmail = '{{ request.user.email }}';
</script>
{% endblock %}

{% block extra_js %}
<!-- Jitsi Meet API من الخادم المحسن -->
<script src="https://meet.ffmuc.net/external_api.js"></script>

<!-- ملفات JavaScript المحدثة -->
<script src="{% static 'js/jitsi-servers.js' %}"></script>
<script src="{% static 'js/jitsi-screenshare-quality.js' %}"></script>
<script src="{% static 'js/jitsi-integration.js' %}"></script>
<script src="{% static 'js/attendance_monitor.js' %}"></script>

<!-- تحقق من تحميل Jitsi API -->
<script>
window.addEventListener('load', function() {
    if (typeof JitsiMeetExternalAPI === 'undefined') {
        console.error('❌ Jitsi Meet API لم يتم تحميله');
        // محاولة تحميل من مصادر بديلة محسنة
        const fallbackServers = [
            'https://8x8.vc/external_api.js',
            'https://meet.element.io/external_api.js',
            'https://meet.jit.si/external_api.js'
        ];

        let currentServer = 0;
        const tryNextServer = () => {
            if (currentServer < fallbackServers.length) {
                const script = document.createElement('script');
                script.src = fallbackServers[currentServer];
                script.onload = () => console.log(`✅ تم تحميل Jitsi API من: ${fallbackServers[currentServer]}`);
                script.onerror = () => {
                    console.warn(`⚠️ فشل تحميل من: ${fallbackServers[currentServer]}`);
                    currentServer++;
                    tryNextServer();
                };
                document.head.appendChild(script);
            } else {
                console.error('❌ فشل تحميل Jitsi API من جميع الخوادم');
            }
        };
        tryNextServer();
    } else {
        console.log('✅ Jitsi Meet API محمل بنجاح');
    }
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تهيئة صفحة المعلم...');

    // التحقق من وجود الملفات المطلوبة
    if (typeof RealTimeAttendanceMonitor === 'undefined') {
        console.error('❌ RealTimeAttendanceMonitor غير محمل');
        // استخدام نظام بديل
        setupSimpleTeacherJitsi();
        return;
    }

    try {
        // تهيئة نظام المراقبة للمعلم
        const monitor = new RealTimeAttendanceMonitor(
            {{ live_lesson.id }},
            'live',
            '{{ request.user.get_full_name }}'
        );

        // تخصيص حاوي Jitsi للمعلم
        console.log(`🔧 تعيين حاوي Jitsi للمعلم: teacher-jitsi-container`);
        monitor.jitsiContainerId = 'teacher-jitsi-container';
        monitor.startButtonId = 'teacher-start-jitsi';
        monitor.confirmButtonId = 'teacher-confirm-attendance';
        monitor.endButtonId = 'teacher-end-lesson';
        monitor.statusTextId = 'teacher-status-text';
        monitor.statusIndicatorId = 'teacher-status-indicator';
        console.log(`✅ تم تعيين الحاوي: ${monitor.jitsiContainerId}`);

        // إعداد أزرار المعلم
        setupTeacherButtons(monitor);

        console.log('✅ تم تهيئة نظام المراقبة للمعلم');

    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام المراقبة:', error);
        // استخدام نظام بديل
        setupSimpleTeacherJitsi();
    }

    // تحديث حالة الطالب في الوقت الفعلي
    setupStudentStatusMonitoring({{ live_lesson.id }});
});

function setupTeacherButtons(monitor) {
    // زر بدء الحصة
    const startButton = document.getElementById('teacher-start-jitsi');
    if (startButton) {
        startButton.onclick = () => {
            startButton.disabled = true;
            startButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البدء...';

            monitor.startJitsiLesson().then(() => {
                // تفعيل أزرار التحكم بعد البدء
                setTimeout(() => {
                    const confirmBtn = document.getElementById('teacher-confirm-attendance');
                    const endBtn = document.getElementById('teacher-end-lesson');

                    if (confirmBtn) {
                        confirmBtn.disabled = false;
                        confirmBtn.classList.remove('opacity-50');
                    }

                    if (endBtn) {
                        endBtn.disabled = false;
                        endBtn.classList.remove('opacity-50');
                    }

                    // إخفاء زر البدء
                    startButton.style.display = 'none';
                }, 3000);
            }).catch((error) => {
                console.error('خطأ في بدء الحصة:', error);
                startButton.disabled = false;
                startButton.innerHTML = '<i class="fas fa-play ml-2"></i>بدء الحصة';
            });
        };
    }

    // زر تأكيد الحضور
    const confirmButton = document.getElementById('teacher-confirm-attendance');
    if (confirmButton) {
        confirmButton.onclick = () => monitor.confirmAttendance();
    }

    // زر إنهاء الحصة
    const endButton = document.getElementById('teacher-end-lesson');
    if (endButton) {
        endButton.onclick = () => {
            if (confirm('هل أنت متأكد من إنهاء الحصة؟')) {
                monitor.markLeave();
            }
        };
    }
}

function setupStudentStatusMonitoring(lessonId) {
    // مراقبة حالة الطالب كل 10 ثوانِ
    setInterval(() => {
        fetch(`/lessons/api/monitoring/live/${lessonId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.student_monitoring) {
                    updateStudentStatus(data.student_monitoring);
                }
            })
            .catch(error => {
                console.error('خطأ في جلب حالة الطالب:', error);
            });
    }, 10000);
}

function updateStudentStatus(studentData) {
    const statusDisplay = document.getElementById('student-status-display');
    const statusInfo = document.getElementById('student-status-info');

    if (statusDisplay && statusInfo) {
        // تحديث النص
        statusDisplay.textContent = getStatusText(studentData.status);

        // تحديث الأيقونة
        const icon = statusInfo.querySelector('i');
        if (icon) {
            icon.className = `fas fa-circle ${getStatusColor(studentData.status)} ml-1`;
        }
    }
}

function getStatusText(status) {
    const statusTexts = {
        'intended': 'ينوي الانضمام',
        'confirmed': 'مؤكد الحضور',
        'active': 'نشط في الحصة',
        'warning': 'تحذير',
        'critical': 'حالة حرجة',
        'completed': 'مكتمل',
        'suspicious': 'نشاط مشبوه'
    };
    return statusTexts[status] || 'لم ينضم بعد';
}

function getStatusColor(status) {
    const colors = {
        'intended': 'text-yellow-500',
        'confirmed': 'text-green-500',
        'active': 'text-blue-500',
        'warning': 'text-orange-500',
        'critical': 'text-red-500',
        'completed': 'text-gray-500',
        'suspicious': 'text-purple-500'
    };
    return colors[status] || 'text-gray-400';
}

// نظام Jitsi بديل للمعلم
function setupSimpleTeacherJitsi() {
    console.log('🔄 تشغيل نظام Jitsi البديل للمعلم...');

    // التحقق من وجود العناصر المطلوبة
    const startButton = document.getElementById('teacher-start-jitsi');
    const container = document.getElementById('teacher-jitsi-container');

    if (!startButton) {
        console.error('❌ زر البدء غير موجود');
        return;
    }

    if (!container) {
        console.error('❌ حاوي Jitsi غير موجود');
        return;
    }

    console.log('✅ جميع العناصر موجودة، تهيئة الأزرار...');

    if (startButton) {
        startButton.onclick = () => {
            console.log('🎯 بدء تشغيل Jitsi للمعلم...');

            // تحديث حالة الزر
            startButton.disabled = true;
            startButton.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البدء...';

            // تحديث مؤشر الحالة
            updateTeacherStatus('intended', 'جاري تشغيل Jitsi...');

            // تشغيل Jitsi
            startTeacherJitsi().then(() => {
                console.log('✅ تم تشغيل Jitsi بنجاح');

                // تحديث الحالة
                updateTeacherStatus('active', 'نشط في الحصة');

                // تفعيل أزرار التحكم
                setTimeout(() => {
                    enableTeacherControls();
                    startButton.style.display = 'none';
                }, 2000);

            }).catch((error) => {
                console.error('❌ خطأ في تشغيل Jitsi:', error);

                // إعادة تعيين الزر
                startButton.disabled = false;
                startButton.innerHTML = '<i class="fas fa-play ml-2"></i>بدء الحصة';
                updateTeacherStatus('error', 'خطأ في التشغيل');
            });
        };
    }
}

async function startTeacherJitsi() {
    return new Promise((resolve, reject) => {
        try {
            // التحقق من تحميل Jitsi API
            if (typeof JitsiMeetExternalAPI === 'undefined') {
                throw new Error('Jitsi Meet API غير محمل');
            }

            const container = document.getElementById('teacher-jitsi-container');
            if (!container) {
                throw new Error('حاوي Jitsi غير موجود');
            }

            console.log('🎯 بدء إنشاء Jitsi API...');

            // إعدادات Jitsi للمعلم مع الخادم المحسن
            const domain = 'meet.ffmuc.net'; // خادم عالي الجودة بدون قيود
            const roomName = `lesson_live_{{ live_lesson.id }}`;
            const displayName = '{{ request.user.get_full_name }} (معلم)';

            const options = {
                roomName: roomName,
                width: '100%',
                height: '100%',
                parentNode: container,
                userInfo: {
                    displayName: displayName
                },
                configOverwrite: {
                    // إعدادات الجودة الفائقة - أقصى ما يمكن
                    resolution: 1440,
                    constraints: {
                        video: {
                            height: { ideal: 2160, max: 2160, min: 1440 }, // دعم 4K
                            width: { ideal: 3840, max: 3840, min: 2560 },  // دعم 4K
                            frameRate: { ideal: 60, max: 60, min: 30 }     // 60 fps
                        }
                    },

                    // إعدادات مشاركة الشاشة بأقصى جودة ممكنة
                    desktopSharingFrameRate: {
                        min: 30,
                        max: 60  // 60 fps
                    },

                    // جودة مشاركة الشاشة - أقصى إعدادات
                    videoQuality: {
                        maxBitratesVideo: {
                            low: 500000,
                            standard: 1000000,
                            high: 3000000,
                            ssHigh: 12000000,  // 12 Mbps لمشاركة الشاشة
                            ultra: 15000000    // 15 Mbps للجودة الفائقة
                        },
                        minHeightForQualityLvl: {
                            360: 'low',
                            720: 'standard',
                            1080: 'high',
                            1440: 'ultra'
                        }
                    },

                    // إعدادات مشاركة الشاشة بأقصى جودة
                    screenshare: {
                        bitrate: 12000000,  // 12 Mbps
                        maxBitrate: 15000000, // 15 Mbps
                        frameRate: 60,      // 60 fps
                        height: 2160,       // 4K
                        width: 3840,        // 4K
                        quality: 'ultra'
                    },

                    // إعدادات المعلم كمشرف
                    startWithAudioMuted: false,
                    startWithVideoMuted: false,
                    enableWelcomePage: false,
                    prejoinPageEnabled: false,
                    requireDisplayName: true,
                    enableUserRolesBasedOnToken: true,
                    moderatorPassword: 'teacher_mod_2024',

                    // إعدادات الأداء
                    enableLayerSuspension: true,
                    channelLastN: -1,
                    enableTileView: true,

                    // إعدادات متقدمة لمشاركة الشاشة
                    p2p: {
                        enabled: false  // تعطيل P2P لضمان جودة أفضل
                    },

                    // إعدادات الترميز المحسنة لأقصى جودة
                    videoCodecPreferenceOrder: ['AV1', 'VP9', 'VP8', 'H264'],

                    // إعدادات البث المحسنة لأقصى جودة
                    enableSimulcast: false,  // تعطيل لضمان أقصى جودة
                    maxFullResolutionParticipants: -1, // جميع المشاركين بأقصى جودة

                    // إعدادات مشاركة الشاشة المتقدمة
                    desktopSharingChromeExtId: null,
                    desktopSharingChromeDisabled: false,
                    desktopSharingFirefoxDisabled: false,

                    // إعدادات الأمان
                    disableModeratorIndicator: false,
                    startScreenSharing: false,
                    enableEmailInStats: false,
                    enableInsecureRoomNameWarning: false,
                    disableThirdPartyRequests: true,

                    // إعدادات التسجيل
                    fileRecordingsEnabled: true,
                    liveStreamingEnabled: false,
                    transcribingEnabled: false
                },
                interfaceConfigOverwrite: {
                    // أزرار التحكم الكاملة للمعلم
                    TOOLBAR_BUTTONS: [
                        'microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen',
                        'fodeviceselection', 'hangup', 'profile', 'chat', 'recording',
                        'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',
                        'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
                        'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone',
                        'security'
                    ],
                    SETTINGS_SECTIONS: ['devices', 'language', 'moderator', 'profile', 'calendar', 'sounds'],

                    // إخفاء العلامات المائية
                    SHOW_JITSI_WATERMARK: false,
                    SHOW_WATERMARK_FOR_GUESTS: false,
                    SHOW_BRAND_WATERMARK: false,
                    BRAND_WATERMARK_LINK: '',
                    SHOW_POWERED_BY: false,
                    SHOW_PROMOTIONAL_CLOSE_PAGE: false,
                    SHOW_CHROME_EXTENSION_BANNER: false,

                    // إعدادات إضافية
                    NATIVE_APP_NAME: 'Qurania Academy',
                    PROVIDER_NAME: 'Qurania Academy',
                    MOBILE_APP_PROMO: false,
                    TOOLBAR_TIMEOUT: 4000,
                    VIDEO_LAYOUT_FIT: 'both'
                }
            };

            console.log('📋 إعدادات Jitsi:', {
                domain: domain,
                roomName: roomName,
                displayName: displayName,
                containerExists: !!container
            });

            // إنشاء API Jitsi
            console.log('🔧 إنشاء JitsiMeetExternalAPI...');
            const api = new JitsiMeetExternalAPI(domain, options);

            console.log('✅ تم إنشاء API بنجاح، انتظار الأحداث...');

            // مراقبة الأحداث
            api.addEventListener('videoConferenceJoined', () => {
                console.log('✅ انضم المعلم للحصة');
                resolve(api);
            });

            api.addEventListener('videoConferenceLeft', () => {
                console.log('👋 غادر المعلم الحصة');
            });

            api.addEventListener('participantJoined', (participant) => {
                console.log('👤 انضم مشارك جديد:', participant);
            });

            // حفظ مرجع API
            window.teacherJitsiApi = api;

            // تطبيق إعدادات جودة مشاركة الشاشة
            if (window.jitsiScreenshareQuality) {
                window.jitsiScreenshareQuality.applyScreenshareQuality(api);
                window.jitsiScreenshareQuality.monitorScreenshareQuality(api);
                console.log('✅ تم تطبيق إعدادات جودة مشاركة الشاشة');
            }

            // timeout للتأكد من التحميل
            setTimeout(() => {
                if (!window.teacherJitsiApi) {
                    reject(new Error('انتهت مهلة تحميل Jitsi'));
                }
            }, 10000);

        } catch (error) {
            reject(error);
        }
    });
}

function updateTeacherStatus(status, message) {
    const statusText = document.getElementById('teacher-status-text');
    const statusIndicator = document.getElementById('teacher-status-indicator');

    if (statusText) {
        statusText.textContent = message;
    }

    if (statusIndicator) {
        statusIndicator.className = `w-3 h-3 rounded-full ml-3 ${getStatusColorClass(status)}`;
    }
}

function getStatusColorClass(status) {
    const colors = {
        'intended': 'bg-yellow-500 animate-pulse',
        'active': 'bg-green-500 animate-pulse',
        'error': 'bg-red-500',
        'completed': 'bg-gray-500'
    };
    return colors[status] || 'bg-gray-400';
}

function enableTeacherControls() {
    const confirmBtn = document.getElementById('teacher-confirm-attendance');
    const endBtn = document.getElementById('teacher-end-lesson');

    if (confirmBtn) {
        confirmBtn.disabled = false;
        confirmBtn.classList.remove('opacity-50');
        confirmBtn.onclick = () => {
            updateTeacherStatus('confirmed', 'تم تأكيد الحضور');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-check ml-2"></i>تم التأكيد';
        };
    }

    if (endBtn) {
        endBtn.disabled = false;
        endBtn.classList.remove('opacity-50');
        endBtn.onclick = () => {
            if (confirm('هل أنت متأكد من إنهاء الحصة؟')) {
                endTeacherLesson();
            }
        };
    }
}

function endTeacherLesson() {
    if (window.teacherJitsiApi) {
        window.teacherJitsiApi.executeCommand('hangup');
    }

    updateTeacherStatus('completed', 'تم إنهاء الحصة');

    // إعادة توجيه بعد 3 ثوانِ
    setTimeout(() => {
        window.location.href = '/dashboard/teacher/schedule/';
    }, 3000);
}
</script>
{% endblock %}
