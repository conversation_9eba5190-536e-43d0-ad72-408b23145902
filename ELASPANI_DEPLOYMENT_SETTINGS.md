# إعدادات النشر المخصصة لـ elaspani

## 🎯 الإعدادات المحدثة لاسم المستخدم: elaspani

### 📋 معلومات الحساب:
- **اسم المستخدم**: `elaspani`
- **اسم قاعدة البيانات**: `elaspani$qurania`
- **رابط الموقع**: `https://elaspani.pythonanywhere.com`

## 🗄️ إعدادات قاعدة البيانات

### في PythonAnywhere Database tab:
```
Database name: elaspani$qurania
Username: elaspani
Password: [كلمة مرور قاعدة البيانات التي اخترتها]
Host: elaspani.mysql.pythonanywhere-services.com
```

## 📁 مسارات الملفات

### مسارات المشروع:
```bash
# مسار المشروع الرئيسي
/home/<USER>/qurania

# مسار الملفات الثابتة
/home/<USER>/qurania/static

# مسار ملفات الوسائط
/home/<USER>/qurania/media

# مسار السجلات
/home/<USER>/qurania/logs

# مسار النسخ الاحتياطية
/home/<USER>/qurania/backups
```

## 🌐 إعدادات Web App

### في PythonAnywhere Web tab:

#### Source code:
```
/home/<USER>/qurania
```

#### WSGI configuration file:
```python
import os
import sys

path = '/home/<USER>/qurania'
if path not in sys.path:
    sys.path.insert(0, path)

os.environ['DJANGO_SETTINGS_MODULE'] = 'qurania.settings_production'

from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
```

#### Virtualenv:
```
/home/<USER>/qurania_env
```

#### Static files:
```
URL: /static/
Directory: /home/<USER>/qurania/static
```

#### Media files:
```
URL: /media/
Directory: /home/<USER>/qurania/media
```

## 🚀 خطوات النشر المحدثة

### 1. إعداد البيئة الافتراضية:
```bash
cd /home/<USER>
python3.10 -m venv qurania_env
source qurania_env/bin/activate
cd qurania
pip install -r requirements.txt
```

### 2. إنشاء المجلدات المطلوبة:
```bash
mkdir -p /home/<USER>/qurania/static
mkdir -p /home/<USER>/qurania/media
mkdir -p /home/<USER>/qurania/logs
mkdir -p /home/<USER>/qurania/backups
chmod 755 /home/<USER>/qurania/static
chmod 755 /home/<USER>/qurania/media
chmod 755 /home/<USER>/qurania/logs
```

### 3. إعداد قاعدة البيانات:
```bash
cd /home/<USER>/qurania
source ../qurania_env/bin/activate
python setup_database.py
```

### 4. جمع الملفات الثابتة:
```bash
python manage.py collectstatic --settings=qurania.settings_production --noinput
```

## 🔧 إعدادات إضافية مطلوبة

### 🔑 كلمة مرور قاعدة البيانات:
في ملف `qurania/settings_production.py`، استبدل:
```python
'PASSWORD': 'your_database_password',  # ← ضع كلمة مرور قاعدة البيانات هنا
```

### 🔐 مفتاح الأمان:
```python
SECRET_KEY = 'your-new-secret-key-here'  # ← أنشئ مفتاح جديد
```

### 📧 إعدادات البريد الإلكتروني:
```python
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your_app_password'  # كلمة مرور التطبيق من Google
```

## 🔍 اختبار الإعدادات

### فحص الاتصال بقاعدة البيانات:
```bash
cd /home/<USER>/qurania
source ../qurania_env/bin/activate
python manage.py dbshell --settings=qurania.settings_production
```

### فحص صحة النظام:
```bash
python health_check.py
```

## 🌐 الوصول للموقع

### الروابط:
- **الموقع الرئيسي**: https://elaspani.pythonanywhere.com
- **لوحة الإدارة**: https://elaspani.pythonanywhere.com/admin/
- **API**: https://elaspani.pythonanywhere.com/api/

### معلومات تسجيل الدخول الافتراضية:
```
المدير:
- اسم المستخدم: admin
- كلمة المرور: admin123456

المعلم التجريبي:
- اسم المستخدم: teacher1
- كلمة المرور: teacher123

الطالب التجريبي:
- اسم المستخدم: student1
- كلمة المرور: student123
```

⚠️ **تذكر تغيير كلمات المرور فور النشر!**

## 🔄 التحديثات المستقبلية

### سكريبت التحديث:
```bash
cd /home/<USER>/qurania
source ../qurania_env/bin/activate
python update_production.py
```

### إعادة تشغيل التطبيق:
- اذهب إلى Web tab في PythonAnywhere
- اضغط على "Reload elaspani.pythonanywhere.com"

## 📊 مراقبة النظام

### فحص السجلات:
```bash
tail -f /home/<USER>/qurania/logs/django.log
```

### فحص استخدام المساحة:
```bash
du -sh /home/<USER>/qurania/*
df -h /home/<USER>
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في قاعدة البيانات:
- تأكد من اسم قاعدة البيانات: `elaspani$qurania`
- تأكد من كلمة المرور
- تأكد من الخادم: `elaspani.mysql.pythonanywhere-services.com`

#### 2. مشاكل الملفات الثابتة:
```bash
python manage.py collectstatic --settings=qurania.settings_production --noinput
```

#### 3. مشاكل الصلاحيات:
```bash
chmod -R 755 /home/<USER>/qurania/static
chmod -R 755 /home/<USER>/qurania/media
```

## ✅ قائمة التحقق النهائية

- [ ] تم إنشاء قاعدة البيانات: `elaspani$qurania`
- [ ] تم تحديث كلمة مرور قاعدة البيانات في الإعدادات
- [ ] تم إنشاء البيئة الافتراضية: `/home/<USER>/qurania_env`
- [ ] تم تثبيت المتطلبات من `requirements.txt`
- [ ] تم تشغيل `setup_database.py`
- [ ] تم جمع الملفات الثابتة
- [ ] تم تكوين Web App في PythonAnywhere
- [ ] تم اختبار الموقع: https://elaspani.pythonanywhere.com
- [ ] تم تغيير كلمات المرور الافتراضية

---

## 🎉 مبروك!

تم تخصيص جميع الإعدادات لاسم المستخدم `elaspani` بنجاح!

الآن يمكنك متابعة خطوات النشر باستخدام هذه الإعدادات المحدثة.
