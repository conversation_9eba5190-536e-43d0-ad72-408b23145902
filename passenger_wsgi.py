#!/usr/bin/python3
"""
ملف WSGI للاستضافة المشتركة (Namecheap)
يستخدم Passenger لتشغيل Django - أكاديمية القرآنية
"""

import sys
import os

# إضافة مسار المشروع
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, SCRIPT_DIR)

# إضافة مسارات إضافية للبحث عن الوحدات
sys.path.insert(0, os.path.join(SCRIPT_DIR, 'qurania'))

# إضافة مسار البيئة الافتراضية (إذا كانت متاحة)
VENV_PATHS = [
    os.path.join(SCRIPT_DIR, 'venv', 'lib', 'python3.9', 'site-packages'),
    os.path.join(SCRIPT_DIR, 'venv', 'lib', 'python3.10', 'site-packages'),
    os.path.join(SCRIPT_DIR, 'venv', 'lib', 'python3.11', 'site-packages'),
]

for venv_path in VENV_PATHS:
    if os.path.exists(venv_path):
        sys.path.insert(0, venv_path)
        break

# تعيين متغير البيئة لإعدادات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania.settings_shared_hosting')

# تهيئة Django
try:
    from django.core.wsgi import get_wsgi_application
    application = get_wsgi_application()
except ImportError:
    # في حالة عدم توفر Django، عرض رسالة خطأ
    def application(environ, start_response):
        status = '500 Internal Server Error'
        headers = [('Content-type', 'text/html; charset=utf-8')]
        start_response(status, headers)
        
        error_message = """
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>خطأ في الخادم</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .error { color: #d32f2f; }
                .info { color: #1976d2; margin-top: 20px; }
            </style>
        </head>
        <body>
            <h1 class="error">خطأ في تحميل Django</h1>
            <p>لم يتم العثور على Django أو هناك مشكلة في الإعدادات</p>
            <div class="info">
                <p>تأكد من:</p>
                <ul style="text-align: right; display: inline-block;">
                    <li>تثبيت Django بشكل صحيح</li>
                    <li>صحة مسار المشروع</li>
                    <li>صحة إعدادات قاعدة البيانات</li>
                </ul>
            </div>
        </body>
        </html>
        """.encode('utf-8')
        
        return [error_message]

# دالة مساعدة لفحص النظام
def check_system():
    """فحص النظام والإعدادات"""
    checks = []
    
    # فحص Python
    checks.append(f"Python Version: {sys.version}")
    
    # فحص Django
    try:
        import django
        checks.append(f"Django Version: {django.get_version()}")
    except ImportError:
        checks.append("Django: غير مثبت")
    
    # فحص المسارات
    checks.append(f"Script Directory: {SCRIPT_DIR}")
    checks.append(f"Python Path: {sys.path[:3]}...")
    
    # فحص متغيرات البيئة
    django_settings = os.environ.get('DJANGO_SETTINGS_MODULE', 'غير محدد')
    checks.append(f"Django Settings: {django_settings}")
    
    return checks

# إذا تم تشغيل الملف مباشرة (للاختبار)
if __name__ == '__main__':
    print("=== فحص نظام أكاديمية القرآنية ===")
    for check in check_system():
        print(check)
    
    print("\n=== اختبار تحميل Django ===")
    try:
        from django.core.wsgi import get_wsgi_application
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'qurania.settings_shared_hosting')
        app = get_wsgi_application()
        print("✅ تم تحميل Django بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تحميل Django: {e}")
