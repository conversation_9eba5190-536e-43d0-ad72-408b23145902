/**
 * تكامل Jitsi Meet API مع نظام المراقبة
 */

class JitsiIntegration {
    constructor(roomName, displayName, monitoringId, isAdminMode = false, containerId = null) {
        this.roomName = roomName;
        this.displayName = displayName;
        this.monitoringId = monitoringId;
        this.isAdminMode = isAdminMode;
        this.containerId = containerId || (isAdminMode ? 'jitsi-meet' : 'jitsi-container');
        this.api = null;
        this.isConnected = false;
        this.participantId = null;
        this.cameraEnabled = false;
        this.micEnabled = false;
        this.connectionQuality = 'unknown';

        this.init();
    }
    
    init() {
        console.log('🚀 بدء تهيئة Jitsi Meet API...');
        console.log(`📦 استخدام حاوي: ${this.containerId}`);
        this.setupJitsiAPI();
        // setupEventListeners سيتم استدعاؤها من setupJitsiAPI بعد إنشاء API
    }
    
    setupJitsiAPI() {
        // التحقق من تحميل Jitsi API
        if (typeof JitsiMeetExternalAPI === 'undefined') {
            console.error('❌ JitsiMeetExternalAPI غير محمل');
            throw new Error('JitsiMeetExternalAPI غير محمل');
        }

        // استخدام خادم Jitsi محسن بدون قيود
        const domain = 'meet.ffmuc.net'; // خادم ألماني عالي الجودة بدون قيود وقت
        // تحديد دور المستخدم (معلم = مشرف، طالب = منتسب)
        const userRole = this.getUserRole();

        // إعدادات محسنة للجودة العالية والأدوار
        const configOverwrite = {
            // إعدادات الجودة العالية
            resolution: 1080,  // جودة 1080p
            constraints: {
                video: {
                    height: { ideal: 1080, max: 1080, min: 720 },
                    width: { ideal: 1920, max: 1920, min: 1280 },
                    frameRate: { ideal: 30, max: 30, min: 15 }
                }
            },

            // إعدادات مشاركة الشاشة عالية الجودة
            desktopSharingFrameRate: {
                min: 15,
                max: 30
            },

            // جودة مشاركة الشاشة
            videoQuality: {
                maxBitratesVideo: {
                    low: 200000,    // 200 kbps
                    standard: 500000, // 500 kbps
                    high: 1500000,   // 1.5 Mbps
                    ssHigh: 2500000  // 2.5 Mbps لمشاركة الشاشة
                },
                minHeightForQualityLvl: {
                    360: 'low',
                    720: 'standard',
                    1080: 'high'
                }
            },

            // إعدادات مشاركة الشاشة المحسنة
            screenshare: {
                bitrate: 2500000,  // 2.5 Mbps لمشاركة الشاشة
                frameRate: 30,     // 30 إطار في الثانية
                height: 1080,      // ارتفاع 1080p
                width: 1920        // عرض 1920p
            },

            // إعدادات عامة محسنة
            startWithAudioMuted: false,  // بدء بالمايك مفتوح
            startWithVideoMuted: false,  // بدء بالكاميرا مفتوحة
            enableWelcomePage: false,
            prejoinPageEnabled: false,
            requireDisplayName: true,
            enableEmailInStats: false,
            enableCalendarIntegration: false,

            // إعدادات الأدوار والصلاحيات
            enableUserRolesBasedOnToken: true,
            moderatorPassword: userRole === 'teacher' ? 'teacher_mod_2024' : undefined,

            // إعدادات خاصة بالدور
            ...(userRole === 'teacher' ? {
                // المعلم كمشرف
                startAudioOnly: false,
                enableNoisyMicDetection: true,
                enableTalkWhileMuted: true,
                disableRemoteMute: false
            } : {
                // الطالب كمنتسب
                startAudioOnly: false,
                enableNoisyMicDetection: false,
                enableTalkWhileMuted: false,
                disableRemoteMute: true  // يمكن للمشرف كتم الطالب
            }),

            // إعدادات الشبكة والأداء
            enableLayerSuspension: true,
            channelLastN: -1,  // عرض جميع المشاركين
            enableTileView: true,

            // إعدادات متقدمة لمشاركة الشاشة
            p2p: {
                enabled: false  // تعطيل P2P لضمان جودة أفضل لمشاركة الشاشة
            },

            // إعدادات الترميز المحسنة
            videoCodecPreferenceOrder: ['VP9', 'VP8', 'H264'],

            // إعدادات البث المحسنة
            enableSimulcast: true,
            maxFullResolutionParticipants: 2,

            // إعدادات مشاركة الشاشة المتقدمة
            desktopSharingChromeExtId: null,
            desktopSharingChromeDisabled: false,
            desktopSharingFirefoxDisabled: false,

            // إعدادات الأمان
            enableLobbyChat: false,
            enableInsecureRoomNameWarning: false,
            disableThirdPartyRequests: true,

            // إعدادات التسجيل والمشاركة
            fileRecordingsEnabled: true,
            liveStreamingEnabled: false,
            transcribingEnabled: false
        };

        // التحقق من وجود الحاوي
        console.log(`🔍 البحث عن حاوي: ${this.containerId}`);
        const containerElement = document.getElementById(this.containerId);
        console.log(`📦 عنصر الحاوي:`, containerElement);

        if (!containerElement) {
            console.error(`❌ حاوي Jitsi غير موجود: ${this.containerId}`);
            console.log(`🔍 جميع العناصر المتاحة:`, document.querySelectorAll('[id*="jitsi"]'));
            throw new Error(`حاوي Jitsi غير موجود: ${this.containerId}`);
        }

        console.log(`✅ تم العثور على الحاوي:`, containerElement);
        console.log(`📏 أبعاد الحاوي: ${containerElement.offsetWidth}x${containerElement.offsetHeight}`);
        console.log(`👁️ حالة العرض: ${getComputedStyle(containerElement).display}`);
        console.log(`🔍 مرئي: ${containerElement.offsetParent !== null}`);
        console.log(`📄 محتوى الحاوي: "${containerElement.innerHTML}"`);

        // تنظيف الحاوي قبل إضافة Jitsi
        containerElement.innerHTML = '';

        // تحسين اسم العرض بناءً على الدور
        const enhancedDisplayName = this.getEnhancedDisplayName(userRole);

        const options = {
            roomName: this.roomName,
            width: '100%',
            height: this.isAdminMode ? '100%' : 700, // زيادة الارتفاع من 600 إلى 700
            parentNode: containerElement,
            userInfo: {
                displayName: enhancedDisplayName,
                email: this.getUserEmail()
            },
            configOverwrite: configOverwrite,
            interfaceConfigOverwrite: {
                // أزرار التحكم الكاملة للمعلم والطالب
                TOOLBAR_BUTTONS: [
                    'microphone',           // مايك
                    'camera',              // كاميرا
                    'closedcaptions',      // ترجمة
                    'desktop',             // مشاركة الشاشة
                    'fullscreen',          // شاشة كاملة
                    'fodeviceselection',   // اختيار الأجهزة
                    'hangup',              // إنهاء المكالمة
                    'profile',             // الملف الشخصي
                    'chat',                // الدردشة
                    'recording',           // التسجيل
                    'livestreaming',       // البث المباشر
                    'etherpad',            // المفكرة المشتركة
                    'sharedvideo',         // فيديو مشترك
                    'settings',            // الإعدادات
                    'raisehand',           // رفع اليد
                    'videoquality',        // جودة الفيديو
                    'filmstrip',           // شريط الفيديو
                    'invite',              // دعوة
                    'feedback',            // التقييم
                    'stats',               // الإحصائيات
                    'shortcuts',           // الاختصارات
                    'tileview',            // عرض البلاط
                    'videobackgroundblur', // تمويه الخلفية
                    'download',            // تحميل
                    'help',                // المساعدة
                    'mute-everyone',       // كتم الجميع (للمشرف)
                    'security'             // الأمان
                ],

                // أقسام الإعدادات المتاحة
                SETTINGS_SECTIONS: [
                    'devices',      // الأجهزة
                    'language',     // اللغة
                    'moderator',    // المشرف
                    'profile',      // الملف الشخصي
                    'calendar',     // التقويم
                    'sounds'        // الأصوات
                ],

                // إخفاء العلامات المائية والإعلانات
                SHOW_JITSI_WATERMARK: false,
                SHOW_WATERMARK_FOR_GUESTS: false,
                SHOW_BRAND_WATERMARK: false,
                BRAND_WATERMARK_LINK: '',
                SHOW_POWERED_BY: false,
                SHOW_PROMOTIONAL_CLOSE_PAGE: false,
                SHOW_CHROME_EXTENSION_BANNER: false,

                // إعدادات إضافية للواجهة
                DISABLE_JOIN_LEAVE_NOTIFICATIONS: false,
                DISABLE_PRESENCE_STATUS: false,
                DISABLE_RINGING: false,
                ENABLE_DIAL_OUT: false,
                ENABLE_FEEDBACK_ANIMATION: true,
                FILM_STRIP_MAX_HEIGHT: 120,
                GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false,
                HIDE_INVITE_MORE_HEADER: false,
                INITIAL_TOOLBAR_TIMEOUT: 20000,
                JITSI_WATERMARK_LINK: '',
                LIVE_STREAMING_HELP_LINK: '',
                MAXIMUM_ZOOMING_COEFFICIENT: 1.3,
                MOBILE_APP_PROMO: false,
                NATIVE_APP_NAME: 'Qurania Academy',
                OPTIMAL_BROWSERS: ['chrome', 'chromium', 'firefox', 'nwjs', 'electron', 'safari'],
                POLICY_LOGO: null,
                PROVIDER_NAME: 'Qurania Academy',
                RANDOM_AVATAR_URL_PREFIX: false,
                RANDOM_AVATAR_URL_SUFFIX: false,
                RECENT_LIST_ENABLED: true,
                REMOTE_THUMBNAIL_MENU_BUTTON_MAX_WIDTH: 25,
                SHOW_DEEP_LINKING_IMAGE: false,
                SUPPORT_URL: '',
                TOOLBAR_TIMEOUT: 4000,
                UNSUPPORTED_BROWSER_URL: '',
                VIDEO_LAYOUT_FIT: 'both',

                // إعدادات إضافية لضمان ظهور أزرار التحكم
                TOOLBAR_ALWAYS_VISIBLE: false,
                DISABLE_FOCUS_INDICATOR: false,
                ENABLE_CLOSE_PAGE: false,
                HIDE_DEEP_LINKING_LOGO: true,
                LANG_DETECTION: true,
                LOCAL_THUMBNAIL_RATIO: 16 / 9,
                MAXIMUM_ZOOMING_COEFFICIENT: 1.3,
                TOOLBAR_HEIGHT: 50
            }
        };
        
        console.log('🔧 إنشاء JitsiMeetExternalAPI مع الخيارات:', options);

        // تأخير قصير للتأكد من أن DOM جاهز
        setTimeout(() => {
            try {
                this.api = new JitsiMeetExternalAPI(domain, options);
                console.log('✅ تم إنشاء Jitsi API');

                // إعداد مستمعي الأحداث بعد إنشاء API
                this.setupEventListeners();

                // تطبيق إعدادات جودة مشاركة الشاشة
                this.setupScreenshareQuality();

                // ضمان ظهور أزرار التحكم
                this.ensureToolbarVisibility();
            } catch (error) {
                console.error('❌ خطأ في إنشاء JitsiMeetExternalAPI:', error);
                throw error;
            }
        }, 100);
    }
    
    setupEventListeners() {
        if (!this.api) return;
        
        // أحداث الاتصال
        this.api.addEventListener('videoConferenceJoined', (data) => {
            console.log('✅ تم الانضمام للمؤتمر:', data);
            this.isConnected = true;
            this.participantId = data.id;
            this.handleParticipantJoined();
        });
        
        this.api.addEventListener('videoConferenceLeft', () => {
            console.log('👋 تم مغادرة المؤتمر');
            this.isConnected = false;
            this.handleParticipantLeft();
        });
        
        // أحداث الكاميرا والمايك
        this.api.addEventListener('audioMuteStatusChanged', (data) => {
            console.log('🎤 تغيير حالة المايك:', data);
            this.micEnabled = !data.muted;
            this.handleMicToggle(this.micEnabled);
        });
        
        this.api.addEventListener('videoMuteStatusChanged', (data) => {
            console.log('📹 تغيير حالة الكاميرا:', data);
            this.cameraEnabled = !data.muted;
            this.handleCameraToggle(this.cameraEnabled);
        });
        
        // أحداث جودة الاتصال
        this.api.addEventListener('participantConnectionStatusChanged', (data) => {
            console.log('📶 تغيير جودة الاتصال:', data);
            this.connectionQuality = data.connectionQuality;
        });
        
        // أحداث المشاركين
        this.api.addEventListener('participantJoined', (data) => {
            console.log('👤 انضمام مشارك جديد:', data);
        });
        
        this.api.addEventListener('participantLeft', (data) => {
            console.log('👤 مغادرة مشارك:', data);
        });
        
        // أحداث الأخطاء
        this.api.addEventListener('errorOccurred', (error) => {
            console.error('❌ خطأ في Jitsi:', error);
        });
        
        // مراقبة التركيز على iframe
        this.setupIframeFocusMonitoring();
    }
    
    setupIframeFocusMonitoring() {
        // انتظار إنشاء iframe
        const checkForIframe = () => {
            const iframe = document.querySelector(`#${this.containerId} iframe`);
            if (!iframe) {
                console.log('⏳ انتظار إنشاء iframe...');
                setTimeout(checkForIframe, 500);
                return;
            }

            console.log('✅ تم العثور على iframe، إعداد مراقبة التركيز...');

            // مراقبة التركيز على iframe
            iframe.addEventListener('focus', () => {
                console.log('🎯 التركيز على iframe');
                this.handleIframeFocus(true);
            });

            iframe.addEventListener('blur', () => {
                console.log('🎯 فقدان التركيز من iframe');
                this.handleIframeFocus(false);
            });

            // مراقبة النقر على iframe
            iframe.addEventListener('click', () => {
                this.handleIframeFocus(true);
            });
        };

        checkForIframe();
    }

    ensureToolbarVisibility() {
        // انتظار تحميل iframe ثم ضمان ظهور أزرار التحكم
        setTimeout(() => {
            if (this.api) {
                try {
                    // إظهار شريط الأدوات
                    this.api.executeCommand('toggleToolbar');

                    // إضافة CSS مخصص لضمان ظهور الأزرار
                    const iframe = document.querySelector(`#${this.containerId} iframe`);
                    if (iframe && iframe.contentDocument) {
                        const style = iframe.contentDocument.createElement('style');
                        style.textContent = `
                            .toolbox {
                                bottom: 10px !important;
                                opacity: 1 !important;
                                visibility: visible !important;
                                z-index: 9999 !important;
                            }
                            .toolbox-content {
                                background: rgba(0, 0, 0, 0.8) !important;
                                border-radius: 8px !important;
                                padding: 8px !important;
                            }
                            .toolbox-button {
                                margin: 0 4px !important;
                                min-width: 40px !important;
                                min-height: 40px !important;
                            }
                        `;
                        iframe.contentDocument.head.appendChild(style);
                        console.log('✅ تم تطبيق CSS مخصص لأزرار التحكم');
                    }
                } catch (error) {
                    console.warn('⚠️ لا يمكن الوصول لـ iframe content:', error);
                }
            }
        }, 3000);
    }

    setupScreenshareQuality() {
        if (!this.api) {
            console.warn('⚠️ Jitsi API غير متوفر لإعداد جودة مشاركة الشاشة');
            return;
        }

        console.log('🎯 إعداد جودة مشاركة الشاشة المحسنة...');

        // تطبيق إعدادات الجودة العالية
        if (window.jitsiScreenshareQuality) {
            window.jitsiScreenshareQuality.applyScreenshareQuality(this.api);
            window.jitsiScreenshareQuality.monitorScreenshareQuality(this.api);
        }

        // مراقبة أحداث مشاركة الشاشة
        this.api.addEventListener('screenSharingStatusChanged', (event) => {
            if (event.on) {
                console.log('🖥️ بدء مشاركة الشاشة - تطبيق إعدادات الجودة العالية');
                this.optimizeForScreenshare();
            } else {
                console.log('🖥️ إيقاف مشاركة الشاشة - العودة للإعدادات العادية');
                this.restoreNormalQuality();
            }
        });

        console.log('✅ تم إعداد مراقبة جودة مشاركة الشاشة');
    }

    optimizeForScreenshare() {
        try {
            // تطبيق أقصى جودة لمشاركة الشاشة
            this.api.executeCommand('setVideoQuality', 'high');

            // تعطيل تقليل الجودة التلقائي
            this.api.executeCommand('setVideoConstraints', {
                'lastN': -1,
                'selectedEndpoints': [],
                'onStageEndpoints': [],
                'defaultConstraints': {
                    'maxHeight': 1080,
                    'maxWidth': 1920
                }
            });

            console.log('🎯 تم تحسين الإعدادات لمشاركة الشاشة');

        } catch (error) {
            console.warn('⚠️ لا يمكن تطبيق بعض إعدادات الجودة:', error);
        }
    }

    restoreNormalQuality() {
        try {
            // العودة للإعدادات العادية
            this.api.executeCommand('setVideoQuality', 'high');
            console.log('🔄 تم استعادة الإعدادات العادية');
        } catch (error) {
            console.warn('⚠️ خطأ في استعادة الإعدادات:', error);
        }
    }

    getUserRole() {
        // تحديد دور المستخدم بناءً على السياق
        if (this.isAdminMode) {
            return 'admin';
        }

        // التحقق من دور المستخدم من الصفحة
        if (window.userRole) {
            return window.userRole; // 'teacher' أو 'student'
        }

        // التحقق من URL أو عناصر الصفحة
        if (window.location.pathname.includes('/teacher/')) {
            return 'teacher';
        } else if (window.location.pathname.includes('/student/')) {
            return 'student';
        }

        // افتراضي
        return 'student';
    }

    getEnhancedDisplayName(userRole) {
        let roleName = '';
        let roleIcon = '';

        switch(userRole) {
            case 'teacher':
                roleName = 'معلم';
                roleIcon = '👨‍🏫';
                break;
            case 'student':
                roleName = 'طالب';
                roleIcon = '👨‍🎓';
                break;
            case 'admin':
                roleName = 'مدير النظام (مراقب)';
                roleIcon = '👨‍💼';
                break;
            default:
                roleName = 'مشارك';
                roleIcon = '👤';
        }

        return `${roleIcon} ${this.displayName} (${roleName})`;
    }

    getUserEmail() {
        // محاولة الحصول على البريد الإلكتروني من السياق
        if (window.userEmail) {
            return window.userEmail;
        }

        // البحث في عناصر الصفحة
        const emailElement = document.querySelector('[data-user-email]');
        if (emailElement) {
            return emailElement.getAttribute('data-user-email');
        }

        return '';
    }

    async handleParticipantJoined() {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_participant_joined',
                    monitoring_id: this.monitoringId,
                    participant_id: this.participantId
                })
            });
            
            const data = await response.json();
            console.log('✅ تم تسجيل الانضمام:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل الانضمام:', error);
        }
    }
    
    async handleParticipantLeft() {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_participant_left',
                    monitoring_id: this.monitoringId
                })
            });
            
            const data = await response.json();
            console.log('✅ تم تسجيل المغادرة:', data);
            
            // عرض نتائج التحقق من الحصة
            if (data.session_valid) {
                this.showCompletionMessage('تم إكمال الحصة بنجاح!', 'success');
            } else {
                this.showCompletionMessage('لم يتم استيفاء متطلبات الحصة', 'warning');
            }
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل المغادرة:', error);
        }
    }
    
    async handleCameraToggle(isEnabled) {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_camera_toggle',
                    monitoring_id: this.monitoringId,
                    is_enabled: isEnabled
                })
            });
            
            const data = await response.json();
            console.log('📹 تم تسجيل تغيير الكاميرا:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل تغيير الكاميرا:', error);
        }
    }
    
    async handleMicToggle(isEnabled) {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_mic_toggle',
                    monitoring_id: this.monitoringId,
                    is_enabled: isEnabled
                })
            });
            
            const data = await response.json();
            console.log('🎤 تم تسجيل تغيير المايك:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل تغيير المايك:', error);
        }
    }
    
    async handleIframeFocus(hasFocus) {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'iframe_focus_change',
                    monitoring_id: this.monitoringId,
                    has_focus: hasFocus
                })
            });
            
            const data = await response.json();
            console.log('🎯 تم تسجيل تغيير التركيز:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل تغيير التركيز:', error);
        }
    }
    
    getJitsiData() {
        return {
            isConnected: this.isConnected,
            participantId: this.participantId,
            cameraEnabled: this.cameraEnabled,
            micEnabled: this.micEnabled,
            connectionQuality: this.connectionQuality,
            participantCount: this.api ? this.api.getNumberOfParticipants() : 0
        };
    }
    
    showCompletionMessage(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-warning';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <strong>${message}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.getElementById('completion-messages');
        if (container) {
            container.innerHTML = alertHtml;
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }
    
    // دوال التحكم
    hangUp() {
        if (this.api) {
            this.api.executeCommand('hangup');
        }
    }
    
    toggleCamera() {
        if (this.api) {
            this.api.executeCommand('toggleVideo');
        }
    }
    
    toggleMic() {
        if (this.api) {
            this.api.executeCommand('toggleAudio');
        }
    }
    
    dispose() {
        if (this.api) {
            this.api.dispose();
            this.api = null;
        }
    }
}
