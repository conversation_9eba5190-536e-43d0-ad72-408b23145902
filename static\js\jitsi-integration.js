/**
 * تكامل Jitsi Meet API مع نظام المراقبة
 */

class JitsiIntegration {
    constructor(roomName, displayName, monitoringId, isAdminMode = false, containerId = null) {
        this.roomName = roomName;
        this.displayName = displayName;
        this.monitoringId = monitoringId;
        this.isAdminMode = isAdminMode;
        this.containerId = containerId || (isAdminMode ? 'jitsi-meet' : 'jitsi-container');
        this.api = null;
        this.isConnected = false;
        this.participantId = null;
        this.cameraEnabled = false;
        this.micEnabled = false;
        this.connectionQuality = 'unknown';

        this.init();
    }
    
    init() {
        console.log('🚀 بدء تهيئة Jitsi Meet API...');
        console.log(`📦 استخدام حاوي: ${this.containerId}`);
        this.setupJitsiAPI();
        this.setupEventListeners();
    }
    
    setupJitsiAPI() {
        const domain = 'meet.jit.si';
        // إعدادات مختلفة للمدير
        const configOverwrite = this.isAdminMode ? {
            startWithAudioMuted: true,
            startWithVideoMuted: true,  // المدير يبدأ بكاميرا مغلقة
            enableWelcomePage: false,
            prejoinPageEnabled: false,
            disableModeratorIndicator: false,
            startScreenSharing: false,
            enableEmailInStats: false,
            readOnlyName: true,  // منع تغيير الاسم
            enableUserRolesBasedOnToken: false
        } : {
            startWithAudioMuted: true,
            startWithVideoMuted: false,
            enableWelcomePage: false,
            prejoinPageEnabled: false,
            disableModeratorIndicator: false,
            startScreenSharing: false,
            enableEmailInStats: false
        };

        // التحقق من وجود الحاوي
        const containerElement = document.getElementById(this.containerId);
        if (!containerElement) {
            console.error(`❌ حاوي Jitsi غير موجود: ${this.containerId}`);
            throw new Error(`حاوي Jitsi غير موجود: ${this.containerId}`);
        }

        const options = {
            roomName: this.roomName,
            width: '100%',
            height: this.isAdminMode ? '100%' : 600,
            parentNode: containerElement,
            userInfo: {
                displayName: this.displayName
            },
            configOverwrite: configOverwrite,
            interfaceConfigOverwrite: {
                TOOLBAR_BUTTONS: [
                    'microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen',
                    'fodeviceselection', 'hangup', 'profile', 'chat', 'recording',
                    'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',
                    'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',
                    'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone'
                ],
                SETTINGS_SECTIONS: ['devices', 'language', 'moderator', 'profile', 'calendar'],
                SHOW_JITSI_WATERMARK: false,
                SHOW_WATERMARK_FOR_GUESTS: false,
                SHOW_BRAND_WATERMARK: false,
                BRAND_WATERMARK_LINK: '',
                SHOW_POWERED_BY: false,
                SHOW_PROMOTIONAL_CLOSE_PAGE: false,
                SHOW_CHROME_EXTENSION_BANNER: false
            }
        };
        
        this.api = new JitsiMeetExternalAPI(domain, options);
        console.log('✅ تم إنشاء Jitsi API');
    }
    
    setupEventListeners() {
        if (!this.api) return;
        
        // أحداث الاتصال
        this.api.addEventListener('videoConferenceJoined', (data) => {
            console.log('✅ تم الانضمام للمؤتمر:', data);
            this.isConnected = true;
            this.participantId = data.id;
            this.handleParticipantJoined();
        });
        
        this.api.addEventListener('videoConferenceLeft', () => {
            console.log('👋 تم مغادرة المؤتمر');
            this.isConnected = false;
            this.handleParticipantLeft();
        });
        
        // أحداث الكاميرا والمايك
        this.api.addEventListener('audioMuteStatusChanged', (data) => {
            console.log('🎤 تغيير حالة المايك:', data);
            this.micEnabled = !data.muted;
            this.handleMicToggle(this.micEnabled);
        });
        
        this.api.addEventListener('videoMuteStatusChanged', (data) => {
            console.log('📹 تغيير حالة الكاميرا:', data);
            this.cameraEnabled = !data.muted;
            this.handleCameraToggle(this.cameraEnabled);
        });
        
        // أحداث جودة الاتصال
        this.api.addEventListener('participantConnectionStatusChanged', (data) => {
            console.log('📶 تغيير جودة الاتصال:', data);
            this.connectionQuality = data.connectionQuality;
        });
        
        // أحداث المشاركين
        this.api.addEventListener('participantJoined', (data) => {
            console.log('👤 انضمام مشارك جديد:', data);
        });
        
        this.api.addEventListener('participantLeft', (data) => {
            console.log('👤 مغادرة مشارك:', data);
        });
        
        // أحداث الأخطاء
        this.api.addEventListener('errorOccurred', (error) => {
            console.error('❌ خطأ في Jitsi:', error);
        });
        
        // مراقبة التركيز على iframe
        this.setupIframeFocusMonitoring();
    }
    
    setupIframeFocusMonitoring() {
        const iframe = document.querySelector(`#${this.containerId} iframe`);
        if (!iframe) return;
        
        // مراقبة التركيز على iframe
        iframe.addEventListener('focus', () => {
            console.log('🎯 التركيز على iframe');
            this.handleIframeFocus(true);
        });
        
        iframe.addEventListener('blur', () => {
            console.log('🎯 فقدان التركيز من iframe');
            this.handleIframeFocus(false);
        });
        
        // مراقبة النقر على iframe
        iframe.addEventListener('click', () => {
            this.handleIframeFocus(true);
        });
    }
    
    async handleParticipantJoined() {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_participant_joined',
                    monitoring_id: this.monitoringId,
                    participant_id: this.participantId
                })
            });
            
            const data = await response.json();
            console.log('✅ تم تسجيل الانضمام:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل الانضمام:', error);
        }
    }
    
    async handleParticipantLeft() {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_participant_left',
                    monitoring_id: this.monitoringId
                })
            });
            
            const data = await response.json();
            console.log('✅ تم تسجيل المغادرة:', data);
            
            // عرض نتائج التحقق من الحصة
            if (data.session_valid) {
                this.showCompletionMessage('تم إكمال الحصة بنجاح!', 'success');
            } else {
                this.showCompletionMessage('لم يتم استيفاء متطلبات الحصة', 'warning');
            }
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل المغادرة:', error);
        }
    }
    
    async handleCameraToggle(isEnabled) {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_camera_toggle',
                    monitoring_id: this.monitoringId,
                    is_enabled: isEnabled
                })
            });
            
            const data = await response.json();
            console.log('📹 تم تسجيل تغيير الكاميرا:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل تغيير الكاميرا:', error);
        }
    }
    
    async handleMicToggle(isEnabled) {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'jitsi_mic_toggle',
                    monitoring_id: this.monitoringId,
                    is_enabled: isEnabled
                })
            });
            
            const data = await response.json();
            console.log('🎤 تم تسجيل تغيير المايك:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل تغيير المايك:', error);
        }
    }
    
    async handleIframeFocus(hasFocus) {
        try {
            const response = await fetch('/lessons/api/monitoring/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify({
                    action: 'iframe_focus_change',
                    monitoring_id: this.monitoringId,
                    has_focus: hasFocus
                })
            });
            
            const data = await response.json();
            console.log('🎯 تم تسجيل تغيير التركيز:', data);
            
        } catch (error) {
            console.error('❌ خطأ في تسجيل تغيير التركيز:', error);
        }
    }
    
    getJitsiData() {
        return {
            isConnected: this.isConnected,
            participantId: this.participantId,
            cameraEnabled: this.cameraEnabled,
            micEnabled: this.micEnabled,
            connectionQuality: this.connectionQuality,
            participantCount: this.api ? this.api.getNumberOfParticipants() : 0
        };
    }
    
    showCompletionMessage(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-warning';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <strong>${message}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.getElementById('completion-messages');
        if (container) {
            container.innerHTML = alertHtml;
        }
    }
    
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }
    
    // دوال التحكم
    hangUp() {
        if (this.api) {
            this.api.executeCommand('hangup');
        }
    }
    
    toggleCamera() {
        if (this.api) {
            this.api.executeCommand('toggleVideo');
        }
    }
    
    toggleMic() {
        if (this.api) {
            this.api.executeCommand('toggleAudio');
        }
    }
    
    dispose() {
        if (this.api) {
            this.api.dispose();
            this.api = null;
        }
    }
}
