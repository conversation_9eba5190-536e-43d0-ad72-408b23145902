"""
إعدادات الإنتاج للاستضافة المشتركة (Namecheap)
"""

from .settings import *
import os
from pathlib import Path

# تعريف BASE_DIR للاستضافة المشتركة
BASE_DIR = Path(__file__).resolve().parent.parent

# إعدادات الأمان للإنتاج
DEBUG = False

# المضيفين المسموح بهم - استبدل بدومينك
ALLOWED_HOSTS = [
    '*',  # مؤقتاً للاختبار - غيره لدومينك الفعلي
    'marketation.online',
    'www.marketation.online',
    'subdomain.yourdomain.com',
    'localhost',
    '127.0.0.1',
]

# إعدادات قاعدة البيانات للاستضافة المشتركة
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'markoivw_qurania',  # اسم قاعدة البيانات
        'USER': 'markoivw_elasapni',  # مستخدم قاعدة البيانات
        'PASSWORD': '0122931008aA@',    # كلمة مرور قاعدة البيانات
        'HOST': 'localhost',  # أو IP الخادم
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}

# إعدادات الملفات الثابتة
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'public_html', 'static')

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'public_html', 'media')

# إعدادات الأمان للاستضافة المشتركة
SECURE_SSL_REDIRECT = False  # قد لا تدعم الاستضافة المشتركة SSL مجاناً
SECURE_PROXY_SSL_HEADER = None
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'SAMEORIGIN'  # أقل تقييداً للاستضافة المشتركة

# إعدادات الجلسات
SESSION_COOKIE_SECURE = False  # False إذا لم يكن لديك SSL
CSRF_COOKIE_SECURE = False     # False إذا لم يكن لديك SSL
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# إعدادات البريد الإلكتروني للاستضافة المشتركة
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'mail.yourdomain.com'  # خادم البريد الخاص بدومينك
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # بريد إلكتروني من دومينك
EMAIL_HOST_PASSWORD = 'your_email_password'
DEFAULT_FROM_EMAIL = 'أكاديمية القرآنية <<EMAIL>>'

# إعدادات التخزين المؤقت البسيط
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': os.path.join(BASE_DIR, 'cache'),
    }
}

# إعدادات السجلات للاستضافة المشتركة
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'ERROR',  # فقط الأخطاء لتوفير المساحة
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django.log'),
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['file'],
        'level': 'ERROR',
    },
}

# إعدادات Jitsi Meet للاستضافة المشتركة
JITSI_SETTINGS = {
    'DEFAULT_DOMAIN': 'meet.ffmuc.net',
    'FALLBACK_DOMAINS': [
        '8x8.vc',
        'meet.element.io',
        'meet.jit.si'
    ],
    'ROOM_PREFIX': 'qurania_',
    'ENABLE_RECORDING': False,
    'ENABLE_TRANSCRIPTION': False,
    'MAX_PARTICIPANTS': 30,  # أقل للاستضافة المشتركة
    'DEFAULT_QUALITY': 'standard',  # جودة متوسطة
}

# إعدادات الدفع
STRIPE_PUBLISHABLE_KEY = 'pk_live_...'  # مفاتيح الإنتاج
STRIPE_SECRET_KEY = 'sk_live_...'
STRIPE_WEBHOOK_SECRET = 'whsec_...'

# إعدادات PayPal
PAYPAL_CLIENT_ID = 'your_paypal_client_id'
PAYPAL_CLIENT_SECRET = 'your_paypal_client_secret'
PAYPAL_MODE = 'live'  # أو 'sandbox' للاختبار

# مفتاح الأمان - أنشئ مفتاح جديد
SECRET_KEY = 'your-production-secret-key-change-this'

# تعطيل debug toolbar في الإنتاج
try:
    INSTALLED_APPS.remove('debug_toolbar')
except ValueError:
    pass

try:
    MIDDLEWARE.remove('debug_toolbar.middleware.DebugToolbarMiddleware')
except ValueError:
    pass

# إعدادات المنطقة الزمنية
USE_TZ = True
TIME_ZONE = 'Asia/Riyadh'

# إعدادات اللغة
LANGUAGE_CODE = 'ar'
USE_I18N = True
USE_L10N = True

# إعدادات الملفات المرفوعة - محدودة للاستضافة المشتركة
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024   # 5 MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024   # 5 MB
FILE_UPLOAD_PERMISSIONS = 0o644

# إعدادات الجلسات
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24 ساعة

# إعدادات CORS
CORS_ALLOWED_ORIGINS = [
    "https://yourdomain.com",
    "https://www.yourdomain.com",
]

CORS_ALLOW_CREDENTIALS = True

# إعدادات CSP مبسطة للاستضافة المشتركة
CSP_DEFAULT_SRC = ("'self'",)
CSP_SCRIPT_SRC = (
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    "https://meet.ffmuc.net",
    "https://8x8.vc",
    "https://meet.jit.si",
    "https://js.stripe.com",
)
CSP_STYLE_SRC = (
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com",
)
CSP_IMG_SRC = (
    "'self'",
    "data:",
    "https:",
)
CSP_FRAME_SRC = (
    "'self'",
    "https://meet.ffmuc.net",
    "https://8x8.vc",
    "https://meet.jit.si",
    "https://js.stripe.com",
)

# تحسينات الأداء للاستضافة المشتركة
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# إعدادات إضافية للاستضافة المشتركة
APPEND_SLASH = True
PREPEND_WWW = False

# تعطيل بعض الميزات الثقيلة
USE_THOUSAND_SEPARATOR = False

# إعدادات الذاكرة المحدودة
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000
