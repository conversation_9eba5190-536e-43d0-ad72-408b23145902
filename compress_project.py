#!/usr/bin/env python3
"""
سكريبت ضغط مشروع أكاديمية القرآنية للنشر على Namecheap
"""

import os
import zipfile
import fnmatch
from pathlib import Path

def should_exclude(file_path, exclude_patterns):
    """فحص إذا كان يجب استبعاد الملف"""
    file_path_str = str(file_path)
    
    for pattern in exclude_patterns:
        if fnmatch.fnmatch(file_path_str, pattern) or pattern in file_path_str:
            return True
    return False

def compress_project():
    """ضغط المشروع مع استبعاد الملفات غير المطلوبة"""
    
    # الملفات والمجلدات المستبعدة
    exclude_patterns = [
        '*.pyc',
        '__pycache__*',
        '.git*',
        'venv*',
        '.env*',
        '*.log',
        '.vscode*',
        '.idea*',
        'node_modules*',
        '*.sqlite3',
        'db.sqlite3',
        '.DS_Store',
        'Thumbs.db',
        '*.tmp',
        '*.temp',
        'cache*',
        'logs*',
        'backups*',
        '*.zip',
        '*.rar',
        '*.7z',
        'compress_project.py'  # استبعاد هذا الملف نفسه
    ]
    
    # اسم الملف المضغوط
    zip_filename = 'qurania_namecheap.zip'
    
    print("🗜️ بدء ضغط مشروع أكاديمية القرآنية...")
    print(f"📁 اسم الملف المضغوط: {zip_filename}")
    
    # حذف الملف المضغوط إذا كان موجوداً
    if os.path.exists(zip_filename):
        os.remove(zip_filename)
        print(f"🗑️ تم حذف الملف المضغوط السابق")
    
    # إنشاء الملف المضغوط
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        
        # المجلد الحالي
        current_dir = Path('.')
        
        # عداد الملفات
        file_count = 0
        excluded_count = 0
        
        # المرور عبر جميع الملفات
        for root, dirs, files in os.walk(current_dir):
            
            # تحويل المسار إلى Path object
            root_path = Path(root)
            
            # فحص إذا كان المجلد مستبعد
            if should_exclude(root_path, exclude_patterns):
                excluded_count += len(files)
                continue
            
            # إضافة الملفات
            for file in files:
                file_path = root_path / file
                
                # فحص إذا كان الملف مستبعد
                if should_exclude(file_path, exclude_patterns):
                    excluded_count += 1
                    continue
                
                # إضافة الملف للأرشيف
                try:
                    # المسار النسبي في الأرشيف
                    arcname = str(file_path.relative_to(current_dir))
                    zipf.write(file_path, arcname)
                    file_count += 1
                    
                    # طباعة تقدم العملية
                    if file_count % 10 == 0:
                        print(f"📄 تم ضغط {file_count} ملف...")
                        
                except Exception as e:
                    print(f"⚠️ تعذر ضغط الملف {file_path}: {e}")
                    excluded_count += 1
    
    # حساب حجم الملف المضغوط
    zip_size = os.path.getsize(zip_filename)
    zip_size_mb = zip_size / (1024 * 1024)
    
    print("\n" + "="*50)
    print("✅ تم ضغط المشروع بنجاح!")
    print("="*50)
    print(f"📁 اسم الملف: {zip_filename}")
    print(f"📊 عدد الملفات المضغوطة: {file_count}")
    print(f"🗑️ عدد الملفات المستبعدة: {excluded_count}")
    print(f"💾 حجم الملف المضغوط: {zip_size_mb:.2f} MB")
    
    print("\n📋 الملفات المستبعدة:")
    for pattern in exclude_patterns:
        print(f"   - {pattern}")
    
    print(f"\n🚀 الملف جاهز للرفع على Namecheap: {zip_filename}")
    
    return zip_filename

def verify_zip_contents(zip_filename):
    """فحص محتويات الملف المضغوط"""
    print(f"\n🔍 فحص محتويات {zip_filename}...")
    
    try:
        with zipfile.ZipFile(zip_filename, 'r') as zipf:
            file_list = zipf.namelist()
            
            # الملفات المهمة التي يجب أن تكون موجودة
            important_files = [
                'manage.py',
                'passenger_wsgi.py',
                '.htaccess',
                'requirements_shared_hosting.txt',
                'qurania/settings_shared_hosting.py',
                'qurania/settings.py',
                'qurania/urls.py',
                'qurania/wsgi.py'
            ]
            
            print("📄 الملفات المهمة:")
            for file in important_files:
                if file in file_list:
                    print(f"   ✅ {file}")
                else:
                    print(f"   ❌ {file} - مفقود!")
            
            # عرض بعض الملفات الموجودة
            print(f"\n📊 إجمالي الملفات: {len(file_list)}")
            print("📁 أول 10 ملفات:")
            for i, file in enumerate(file_list[:10]):
                print(f"   {i+1}. {file}")
            
            if len(file_list) > 10:
                print(f"   ... و {len(file_list) - 10} ملف آخر")
                
    except Exception as e:
        print(f"❌ خطأ في فحص الملف المضغوط: {e}")

def main():
    """الدالة الرئيسية"""
    print("🗜️ ضاغط مشروع أكاديمية القرآنية")
    print("="*50)
    
    try:
        # ضغط المشروع
        zip_filename = compress_project()
        
        # فحص المحتويات
        verify_zip_contents(zip_filename)
        
        print("\n🎉 العملية مكتملة بنجاح!")
        print(f"📁 الملف المضغوط: {zip_filename}")
        print("🚀 يمكنك الآن رفع الملف على Namecheap")
        
    except Exception as e:
        print(f"❌ خطأ في ضغط المشروع: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()
